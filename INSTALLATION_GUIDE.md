# Venus Auto Fill - Quick Installation Guide

## 🚀 Extension Overview

Venus Auto Fill is now ready for installation! This Chrome extension automates data entry for web applications by fetching attendance data from Google Apps Script and executing customizable automation flows.

## 📁 Extension Structure

Your extension contains the following files:

```
Venus Auto Fill Extension/
├── manifest.json                 # Extension configuration
├── popup.html                   # Main popup interface
├── background.js                # Background service worker
├── content.js                   # Content script for page automation
├── injected.js                  # Advanced DOM manipulation
├── styles/
│   └── popup.css                # Popup styling
├── scripts/
│   └── popup.js                 # Popup functionality
├── icons/
│   ├── icon16.png              # Extension icons (16x16)
│   ├── icon32.png              # Extension icons (32x32)
│   ├── icon48.png              # Extension icons (48x48)
│   └── icon128.png             # Extension icons (128x128)
├── README.md                    # Detailed documentation
├── sample-flows.json            # Example automation flows
├── google-apps-script-sample.js # Google Apps Script template
├── install.bat                  # Windows installation helper
└── verify-extension.js          # Extension verification script
```

## 🔧 Quick Installation Steps

### Step 1: Install the Extension

1. **Open Google Chrome**
2. **Navigate to Extensions Page**
   - Type `chrome://extensions/` in the address bar
   - Or go to Menu → More Tools → Extensions
3. **Enable Developer Mode**
   - Toggle the "Developer mode" switch in the top-right corner
4. **Load the Extension**
   - Click "Load unpacked"
   - Select this folder: `D:\Gawean Rebinmas\Venus Auto Fill\Ekstensi_auto_fill`
   - The extension should appear in your extensions list
5. **Pin the Extension**
   - Click the puzzle piece icon in Chrome toolbar
   - Find "Venus Auto Fill" and click the pin icon

### Step 2: Set Up Google Apps Script

1. **Open Google Apps Script**
   - Go to [script.google.com](https://script.google.com)
   - Sign in with your Google account
2. **Create New Project**
   - Click "New project"
   - Give it a name like "Venus Auto Fill API"
3. **Add the Script Code**
   - Delete the default code
   - Copy all content from `google-apps-script-sample.js`
   - Paste it into the script editor
4. **Deploy as Web App**
   - Click "Deploy" → "New deployment"
   - Choose "Web app" as the type
   - Set "Execute as" to "Me"
   - Set "Who has access" to "Anyone"
   - Click "Deploy"
   - **Copy the Web App URL** (you'll need this for the extension)

### Step 3: Configure the Extension

1. **Click the Venus Auto Fill icon** in Chrome toolbar
2. **Go to Configuration tab**
3. **Enter your settings**:
   - **Target Website URL**: The website where you want to automate data entry
   - **Username/Password**: Your login credentials for the target website
   - **Apps Script URL**: Paste the Web App URL from Step 2
   - **Sheet Name**: Keep as "monthlyGridData_May_2025" or change to your sheet name
4. **Save Configuration**
5. **Test Connection** to verify everything works

## 📊 Data Format Requirements

Your Google Sheets data should be formatted as follows:

| Employee ID | Employee Name | Day 1 | Day 2 | Day 3 | ... | Day 31 |
|-------------|---------------|-------|-------|-------|-----|--------|
| PTRJ.241000089 | Nursamsih | (7) \| (7.5) | (8) \| (0) | (7) \| (1) | ... | (7) \| (0) |
| PTRJ.241000090 | Oktorio Dwi Sakti | (7) \| (7.5) | (8) \| (0) | (7) \| (1) | ... | (7) \| (0) |

**Format explanation:**
- `(7) | (7.5)` means 7 regular hours and 7.5 overtime hours
- `(8) | (0)` means 8 regular hours and 0 overtime hours

## 🔄 Creating Your First Automation Flow

1. **Switch to Flow Definition tab**
2. **Add events** by clicking "Add Event"
3. **Example flow for basic form filling**:
   ```
   Event 1: Click → #employee-search-btn
   Event 2: Input → #employee-id → {employeeId}
   Event 3: Click → #search-submit
   Event 4: Wait → 2000ms
   Event 5: Input → #regular-hours → {day1.regular}
   Event 6: Input → #overtime-hours → {day1.overtime}
   Event 7: Click → #save-btn
   ```
4. **Test elements** using the "Check" button
5. **Save your flow** for reuse

## 🎯 Available Placeholders

Use these placeholders in your input values:

- `{employeeId}` - Employee ID from data
- `{employeeName}` - Employee name from data
- `{day1.regular}` - Regular hours for day 1
- `{day1.overtime}` - Overtime hours for day 1
- `{day2.regular}` - Regular hours for day 2
- `{today}` - Current date (YYYY-MM-DD)
- `{recordIndex}` - Current record number

## ⚡ Running Automation

1. **Load your data** in the Data Preview tab
2. **Navigate to your target website** in the same Chrome tab
3. **Go to Execution tab** in the extension
4. **Click "Run"** to start automation
5. **Monitor progress** with the progress bar and logs
6. **Use Pause/Stop** controls as needed

## 🛠️ Troubleshooting

### Common Issues:

1. **Extension not loading**
   - Ensure all files are in the correct directory
   - Check that Developer mode is enabled
   - Refresh the extensions page

2. **Google Apps Script connection failed**
   - Verify the Web App URL is correct
   - Check that the script is deployed with "Anyone" access
   - Ensure your Google Sheets has the correct data format

3. **Automation not working**
   - Use browser Developer Tools (F12) to check for errors
   - Verify CSS selectors using the "Check" button
   - Ensure the target website is fully loaded before running

4. **Elements not found**
   - Check CSS selectors in browser Developer Tools
   - Use more specific selectors (ID > Class > Attribute)
   - Add wait events after page navigation

## 📞 Support

- **Documentation**: See `README.md` for detailed information
- **Sample Flows**: Check `sample-flows.json` for examples
- **Console Logs**: Press F12 in Chrome to see detailed logs

## 🎉 You're Ready!

Your Venus Auto Fill extension is now installed and configured. Start by:

1. Testing the connection to your Google Apps Script
2. Loading some sample data
3. Creating a simple automation flow
4. Running a test automation on your target website

Happy automating! 🚀
