# Google Apps Script Setup Guide for Venus Auto Fill

## **Enhanced Script Features**

This setup guide now uses the **Enhanced Google Apps Script** (`google-apps-script-enhanced.js`) which includes:

- ✅ **Extension Compatibility**: Full compatibility with Venus Auto Fill extension
- ✅ **Enhanced Daily Grid Sync**: Advanced formatting with 7-hour conditional highlighting
- ✅ **Monthly Grid Support**: Complete monthly data synchronization
- ✅ **Charge Job Integration**: Task, machine, and expense code tracking
- ✅ **Advanced Formatting**: Color-coded attendance status with visual indicators
- ✅ **Multiple Sheet Support**: Dynamic sheet creation for different periods
- ✅ **CORS Enabled**: Proper cross-origin headers for extension access

## **Step-by-Step Deployment Instructions**

### **1. Create and Deploy Enhanced Google Apps Script**

1. **Open Google Apps Script**
   - Go to [script.google.com](https://script.google.com)
   - Click "New Project"

2. **Replace Default Code**
   - Delete all existing code
   - Copy and paste the code from `google-apps-script-enhanced.js` 
   - Save the project (Ctrl+S) with name "Venus Auto Fill Enhanced API"

3. **Deploy as Web App**
   - Click "Deploy" → "New deployment"
   - Click gear icon ⚙️ next to "Type"
   - Select "Web app"
   - Configure deployment:
     - **Description**: "Venus Auto Fill Enhanced API v2.0"
     - **Execute as**: "Me (your email)"
     - **Who has access**: "Anyone"
   - Click "Deploy"
   - **IMPORTANT**: Copy the web app URL (ends with `/exec`)

### **2. Verify Enhanced Deployment**

1. **Test the URL**
   - Open the web app URL in a new browser tab
   - Add `?action=test&sheet=monthlyGridData_May_2025` to the URL
   - You should see a JSON response like:
   ```json
   {
     "success": true,
     "message": "Connection successful",
     "sheetInfo": {
       "name": "monthlyGridData_May_2025",
       "rows": 5,
       "columns": 35,
       "spreadsheetId": "your-spreadsheet-id",
       "spreadsheetName": "Your Spreadsheet Name"
     }
   }
   ```

2. **Test Enhanced Actions**
   - Test available sheets: `?action=getSheets`
   - Test data validation: `?action=validateData&sheet=monthlyGridData_May_2025`

### **3. Configure Your Enhanced Spreadsheet**

1. **Sheet Structure Requirements**
   - **Monthly Grid Sheets**: `monthlyGridData_[Month]_[Year]` format
   - **Employee columns**: No, Employee ID, Employee Name
   - **Daily columns**: Day 1-31 with formatted hours `(7.0) | (1.5)`
   - **Summary columns**: Total Days, Regular Hours, Overtime Hours
   - **Charge Job columns**: Task Code, Machine Code, Expense Code

2. **Enhanced Data Format Example**
   ```
   | No | Employee ID | Employee Name | 1(Mon) | 2(Tue) | ... | Total Days | Regular Hours | Overtime Hours |
   |----|-------------|---------------|--------|--------|-----|------------|---------------|----------------|
   | 1  | EMP001      | John Doe      | (7.0)  | (7.5)|(0.5) | ... | 22    | 154.0         | 12.5           |
   | 2  | EMP002      | Jane Smith    | (8.0)  | (7.0)  | ... | 23    | 161.0         | 8.0            |
   ```

### **4. Enhanced Extension Configuration**

1. **Script URL**: Paste the enhanced web app URL (must end with `/exec`)
2. **Sheet Name**: Enter sheet name (e.g., `monthlyGridData_May_2025`)
3. **Test Connection**: Click to verify enhanced setup
4. **Debug Connection**: Use for comprehensive diagnostics

### **5. Enhanced Features & Troubleshooting**

#### **New Enhanced Features**
- **7-Hour Highlighting**: Perfect attendance days highlighted in bright green
- **Enhanced Color Coding**: Different colors for various work hour scenarios
- **Charge Job Support**: Automatic integration of task/machine/expense codes
- **Dynamic Sheet Creation**: Automatic monthly sheet generation
- **Summary Calculations**: Real-time totals for days, regular, and overtime hours

#### **Enhanced Error Messages**
The script now provides more detailed error information:

```javascript
// Enhanced success response
{
  "success": true,
  "data": [...],
  "metadata": {
    "sheetName": "monthlyGridData_May_2025",
    "totalRows": 150,
    "totalColumns": 38,
    "lastUpdated": "2025-01-15T10:30:00.000Z"
  }
}

// Enhanced error response
{
  "success": false,
  "error": "Sheet 'monthlyGridData_May_2025' not found. Available sheets: Sheet1, AttendanceData, MonthlyGridData"
}
```

### **6. Advanced Enhanced Configuration**

#### **Multiple Enhanced Actions Support**
- `test` - Connection testing (Extension compatible)
- `getData` - Fetch attendance data (Extension compatible)
- `sync_attendance` - Sync daily attendance records
- `sync_monthly_grid` - Sync monthly summary data
- `sync_daily_grid` - Enhanced daily grid with formatting
- `getSheets` - List available sheets
- `validateData` - Validate sheet structure
- `clear_data` - Clear attendance data (with confirmation)

#### **Enhanced Conditional Formatting**
- 🟢 **Bright Green**: Exactly 7 hours (Mon-Fri) or 5 hours (Sat)
- 🟩 **Light Green**: Regular work hours without overtime
- 🟨 **Yellow**: Work with overtime hours
- 🟦 **Blue**: Check-in only records
- 🟪 **Purple**: Check-out only records
- 🔴 **Red**: Partial or insufficient hours
- ⚪ **Gray**: Absent or no data
- 🟦 **Light Blue**: OFF days

#### **Enhanced Performance Optimization**
- **Batch Processing**: Efficient handling of large datasets
- **Smart Formatting**: Conditional formatting applied programmatically
- **Dynamic Headers**: Automatic day/month header generation
- **Memory Optimization**: Reduced memory usage for large sheets

### **7. Enhanced Security & Monitoring**

- **Enhanced Logging**: Detailed console logs for debugging
- **Error Tracking**: Comprehensive error reporting with stack traces
- **CORS Security**: Proper cross-origin headers for extension access
- **Data Validation**: Enhanced input validation and sanitization

### **8. Enhanced Debugging Tools**

The extension's "Debug Connection" button now provides enhanced diagnostic information including:
- **Enhanced URL validation**: Checks for proper Google Apps Script deployment
- **Enhanced network testing**: Tests all available actions
- **Enhanced response analysis**: Validates JSON structure and data format
- **Enhanced compatibility check**: Verifies extension-script compatibility

### **9. Enhanced Script Update Process**

When updating the enhanced Google Apps Script:
1. Edit the script code in `google-apps-script-enhanced.js`
2. Copy updated code to Google Apps Script editor
3. Save changes (Ctrl+S)
4. Create new deployment OR update existing deployment
5. Test with extension using "Debug Connection"
6. Verify enhanced features are working

---

## **Enhanced Quick Verification Checklist**

- [ ] Enhanced Google Apps Script deployed as web app
- [ ] URL ends with `/exec`
- [ ] Access set to "Anyone"
- [ ] Execute as "Me"
- [ ] Spreadsheet contains enhanced data format
- [ ] Sheet name matches extension configuration
- [ ] Test connection returns enhanced success response
- [ ] Debug connection shows all enhanced features working

---

## **Enhanced API Endpoints Reference**

| **Action** | **Extension Compatible** | **Description** |
|------------|-------------------------|-----------------|
| `test` | ✅ Yes | Connection testing with sheet info |
| `getData` | ✅ Yes | Fetch data for extension preview |
| `sync_attendance` | ⚠️ Backend | Sync daily attendance records |
| `sync_monthly_grid` | ⚠️ Backend | Sync monthly summary data |
| `sync_daily_grid` | ⚠️ Backend | Enhanced daily grid with formatting |
| `getSheets` | ✅ Yes | List all available sheets |
| `validateData` | ✅ Yes | Validate sheet structure |
| `clear_data` | ⚠️ Backend | Clear data (requires confirmation) |

---

**Need Help?** Use the enhanced "Debug Connection" feature in the extension for comprehensive diagnostic information about your enhanced setup. 