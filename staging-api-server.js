/**
 * Simple Staging API Server for Timesheet Automation Testing
 * Run with: node staging-api-server.js
 * Accessible at: http://localhost:5173/api
 */

const express = require('express');
const cors = require('cors');
const app = express();
const PORT = 5173;

// Middleware
app.use(cors({
    origin: ['chrome-extension://*', 'http://localhost:*'],
    credentials: true
}));
app.use(express.json());

// Sample timesheet data
const sampleTimesheetData = [
    {
        employee_id: 'EMP001',
        employee_name: '<PERSON>',
        date: '2024-01-15',
        check_in: '08:00',
        check_out: '17:30',
        regular_hours: 8.0,
        overtime_hours: 1.5,
        task_code: 'PROJ001',
        machine_code: 'MAC001',
        expense_code: 'EXP001'
    },
    {
        employee_id: 'EMP002',
        employee_name: '<PERSON>',
        date: '2024-01-15',
        check_in: '09:00',
        check_out: '18:00',
        regular_hours: 8.0,
        overtime_hours: 1.0,
        task_code: 'PROJ002',
        machine_code: 'MAC002',
        expense_code: 'EXP002'
    },
    {
        employee_id: 'EMP003',
        employee_name: '<PERSON>',
        date: '2024-01-15',
        check_in: '07:30',
        check_out: '16:30',
        regular_hours: 8.0,
        overtime_hours: 1.0,
        task_code: 'PROJ001',
        machine_code: 'MAC003',
        expense_code: 'EXP001'
    },
    {
        employee_id: 'EMP004',
        employee_name: 'Sarah Wilson',
        date: '2024-01-15',
        check_in: '08:30',
        check_out: '17:00',
        regular_hours: 8.0,
        overtime_hours: 0.5,
        task_code: 'PROJ003',
        machine_code: 'MAC004',
        expense_code: 'EXP003'
    },
    {
        employee_id: 'EMP005',
        employee_name: 'David Brown',
        date: '2024-01-15',
        check_in: '08:00',
        check_out: '18:30',
        regular_hours: 8.0,
        overtime_hours: 2.5,
        task_code: 'PROJ002',
        machine_code: 'MAC005',
        expense_code: 'EXP002'
    }
];

// Routes

// Health check endpoint
app.get('/api/health', (req, res) => {
    console.log('🔍 Health check requested');
    res.json({
        success: true,
        data: {
            name: 'Timesheet Staging API',
            status: 'healthy',
            version: '1.0.0',
            timestamp: new Date().toISOString(),
            endpoints: [
                'GET /api/health',
                'GET /api/staging/data',
                'POST /api/automation/results',
                'POST /api/auth/login'
            ]
        }
    });
});

// Get staging timesheet data
app.get('/api/staging/data', (req, res) => {
    console.log('📊 Staging data requested');
    
    // Simulate some processing delay
    setTimeout(() => {
        res.json({
            success: true,
            data: sampleTimesheetData,
            metadata: {
                totalRecords: sampleTimesheetData.length,
                lastUpdated: new Date().toISOString(),
                source: 'staging_database',
                apiVersion: '1.0.0'
            }
        });
    }, 500);
});

// Alternative endpoint that returns data directly as array
app.get('/api/staging/data/raw', (req, res) => {
    console.log('📊 Raw staging data requested');
    res.json(sampleTimesheetData);
});

// Submit automation results
app.post('/api/automation/results', (req, res) => {
    console.log('📝 Automation results submitted:', req.body);
    
    res.json({
        success: true,
        message: 'Results submitted successfully',
        submissionId: 'SUB_' + Date.now(),
        data: {
            processed: true,
            timestamp: new Date().toISOString(),
            recordsProcessed: req.body.processedRecords || 0
        }
    });
});

// Authentication endpoint (optional)
app.post('/api/auth/login', (req, res) => {
    console.log('🔐 Authentication requested:', req.body.username);
    
    const { username, password, apiKey } = req.body;
    
    // Simple auth validation
    if ((username && password) || apiKey) {
        res.json({
            success: true,
            token: 'jwt_token_' + Date.now(),
            expiresIn: 3600,
            user: {
                username: username || 'api_user',
                role: 'automation',
                permissions: ['read_timesheet', 'submit_results']
            }
        });
    } else {
        res.status(401).json({
            success: false,
            message: 'Invalid credentials'
        });
    }
});

// Get available endpoints
app.get('/api/endpoints', (req, res) => {
    res.json({
        success: true,
        data: [
            {
                method: 'GET',
                path: '/api/health',
                description: 'Health check endpoint'
            },
            {
                method: 'GET',
                path: '/api/staging/data',
                description: 'Get timesheet staging data'
            },
            {
                method: 'POST',
                path: '/api/automation/results',
                description: 'Submit automation results'
            },
            {
                method: 'POST',
                path: '/api/auth/login',
                description: 'Authenticate user'
            }
        ]
    });
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error('❌ Server error:', err);
    res.status(500).json({
        success: false,
        error: 'Internal server error',
        message: err.message
    });
});

// 404 handler
app.use('*', (req, res) => {
    console.log(`❓ Unknown endpoint requested: ${req.method} ${req.originalUrl}`);
    res.status(404).json({
        success: false,
        error: 'Endpoint not found',
        message: `${req.method} ${req.originalUrl} is not a valid endpoint`,
        availableEndpoints: [
            'GET /api/health',
            'GET /api/staging/data',
            'POST /api/automation/results',
            'POST /api/auth/login'
        ]
    });
});

// Start server
app.listen(PORT, () => {
    console.log('🚀 Timesheet Staging API Server Started');
    console.log(`📍 Server running at: http://localhost:${PORT}`);
    console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);
    console.log('');
    console.log('📋 Available Endpoints:');
    console.log('  GET  /api/health           - Health check');
    console.log('  GET  /api/staging/data     - Get timesheet data');
    console.log('  POST /api/automation/results - Submit results');
    console.log('  POST /api/auth/login       - Authentication');
    console.log('');
    console.log('🎯 Ready for Chrome Extension Automation Bot!');
    console.log('💡 Use Ctrl+C to stop the server');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down staging API server...');
    process.exit(0);
});

module.exports = app; 