// Venus Auto Fill - Background Service Worker

class VenusAutoFillBackground {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        console.log('Venus Auto Fill background service worker initialized');
    }

    setupEventListeners() {
        // Handle extension installation
        chrome.runtime.onInstalled.addListener((details) => {
            this.handleInstallation(details);
        });

        // Handle messages from popup and content scripts
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });

        // Handle tab updates
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // Handle storage changes
        chrome.storage.onChanged.addListener((changes, namespace) => {
            this.handleStorageChange(changes, namespace);
        });
    }

    handleInstallation(details) {
        if (details.reason === 'install') {
            console.log('Venus Auto Fill installed');
            this.setDefaultConfiguration();
        } else if (details.reason === 'update') {
            console.log('Venus Auto Fill updated');
            this.handleUpdate(details);
        }
    }

    async setDefaultConfiguration() {
        const defaultConfig = {
            scriptUrl: 'https://script.google.com/macros/s/AKfycbzfhUsf1nHUy3ivBdjK-GkzTjcnM6oSUHKDRUDf9r0zKvvEeQcLl7y85nmXctZqRM7rDg/exec',
            sheetName: 'monthlyGridData_May_2025',
            targetUrl: '',
            username: '',
            password: ''
        };

        try {
            await chrome.storage.local.set({ venusConfig: defaultConfig });
            console.log('Default configuration set');
        } catch (error) {
            console.error('Failed to set default configuration:', error);
        }
    }

    handleUpdate(details) {
        console.log(`Venus Auto Fill updated from ${details.previousVersion} to ${chrome.runtime.getManifest().version}`);
        // Handle any migration logic here if needed
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'fetchData':
                    await this.fetchDataFromScript(message, sendResponse);
                    break;

                case 'testConnection':
                    await this.testScriptConnection(message, sendResponse);
                    break;

                case 'executeFlow':
                    await this.executeFlowOnTab(message, sender, sendResponse);
                    break;

                case 'getTabInfo':
                    await this.getTabInfo(sender, sendResponse);
                    break;

                case 'logExecution':
                    this.logExecutionEvent(message);
                    sendResponse({ success: true });
                    break;

                default:
                    console.warn('Unknown message action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async fetchDataFromScript(message, sendResponse) {
        try {
            const { scriptUrl, sheetName } = message;
            
            if (!scriptUrl || !sheetName) {
                throw new Error('Script URL and Sheet Name are required');
            }
            
            const url = `${scriptUrl}?action=getData&sheet=${encodeURIComponent(sheetName)}`;
            
            console.log('Fetching data from:', url);
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                // Add mode and credentials for better CORS handling
                mode: 'cors',
                credentials: 'omit'
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
            }

            const text = await response.text();
            console.log('Raw response:', text);
            
            let data;
            try {
                data = JSON.parse(text);
            } catch (parseError) {
                throw new Error(`Invalid JSON response: ${parseError.message}`);
            }
            
            if (data.success) {
                // Process and validate the data
                const processedData = this.processAttendanceData(data.data);
                console.log('Processed data:', processedData.length, 'records');
                sendResponse({ success: true, data: processedData });
            } else {
                sendResponse({ success: false, error: data.error || 'Unknown error from Google Apps Script' });
            }
        } catch (error) {
            console.error('Fetch data error:', error);
            sendResponse({ success: false, error: `Data fetch failed: ${error.message}` });
        }
    }

    processAttendanceData(rawData) {
        if (!Array.isArray(rawData)) {
            throw new Error('Invalid data format: expected array');
        }

        return rawData.map((row, index) => {
            // Expected format: [Employee ID, Employee Name, Day1, Day2, ..., Day31]
            if (!Array.isArray(row) || row.length < 3) {
                console.warn(`Invalid row ${index}:`, row);
                return null;
            }

            const employeeId = row[0];
            const employeeName = row[1];
            const attendanceData = row.slice(2); // All days data

            // Parse attendance data - format: "(7) | (7.5)" where first is regular hours, second is overtime
            const parsedAttendance = attendanceData.map(dayData => {
                if (!dayData || typeof dayData !== 'string') {
                    return { regular: 0, overtime: 0, raw: dayData };
                }

                const match = dayData.match(/\(([^)]+)\)\s*\|\s*\(([^)]+)\)/);
                if (match) {
                    return {
                        regular: parseFloat(match[1]) || 0,
                        overtime: parseFloat(match[2]) || 0,
                        raw: dayData
                    };
                } else {
                    // Try to parse single number in parentheses
                    const singleMatch = dayData.match(/\(([^)]+)\)/);
                    if (singleMatch) {
                        return {
                            regular: parseFloat(singleMatch[1]) || 0,
                            overtime: 0,
                            raw: dayData
                        };
                    }
                }

                return { regular: 0, overtime: 0, raw: dayData };
            });

            return {
                employeeId,
                employeeName,
                attendanceData: parsedAttendance,
                rawAttendanceData: attendanceData
            };
        }).filter(row => row !== null);
    }

    async testScriptConnection(message, sendResponse) {
        try {
            const { scriptUrl, sheetName } = message;
            const url = `${scriptUrl}?action=test&sheet=${encodeURIComponent(sheetName)}`;
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            sendResponse(data);
        } catch (error) {
            console.error('Test connection error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeFlowOnTab(message, sender, sendResponse) {
        try {
            const { flowEvents, data } = message;
            const tabId = sender.tab.id;

            // Inject the execution script into the tab
            await chrome.scripting.executeScript({
                target: { tabId },
                files: ['injected.js']
            });

            // Send the flow execution command
            const response = await chrome.tabs.sendMessage(tabId, {
                action: 'executeFlow',
                flowEvents,
                data
            });

            sendResponse(response);
        } catch (error) {
            console.error('Execute flow error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async getTabInfo(sender, sendResponse) {
        try {
            const tab = sender.tab;
            sendResponse({
                success: true,
                tabInfo: {
                    id: tab.id,
                    url: tab.url,
                    title: tab.title
                }
            });
        } catch (error) {
            console.error('Get tab info error:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    logExecutionEvent(message) {
        const { event, level, timestamp } = message;
        console.log(`[${timestamp || new Date().toISOString()}] [${level || 'INFO'}] ${event}`);
        
        // Store execution logs for debugging
        chrome.storage.local.get(['venusExecutionLogs'], (result) => {
            const logs = result.venusExecutionLogs || [];
            logs.push({
                event,
                level: level || 'info',
                timestamp: timestamp || new Date().toISOString()
            });
            
            // Keep only last 100 logs
            if (logs.length > 100) {
                logs.splice(0, logs.length - 100);
            }
            
            chrome.storage.local.set({ venusExecutionLogs: logs });
        });
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // Handle tab updates if needed
        if (changeInfo.status === 'complete' && tab.url) {
            // Tab finished loading
            console.log(`Tab ${tabId} finished loading: ${tab.url}`);
        }
    }

    handleStorageChange(changes, namespace) {
        // Handle storage changes if needed
        if (namespace === 'local') {
            for (const key in changes) {
                const change = changes[key];
                console.log(`Storage key "${key}" changed:`, change);
            }
        }
    }
}

// Initialize background service worker
new VenusAutoFillBackground();
