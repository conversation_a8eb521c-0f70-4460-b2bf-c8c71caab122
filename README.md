# Venus Auto Fill - Automated Data Entry Chrome Extension

Venus Auto Fill is a powerful Chrome extension designed for automated data entry in web applications. It fetches attendance data from Google Apps Script and automates form filling with customizable flow definitions.

## Features

### 🔧 Configuration Management
- Store target website URL and login credentials securely
- Configure Google Apps Script integration
- Persistent storage of all settings

### 📊 Data Preview
- Fetch and preview attendance data from Google Sheets
- Parse complex attendance formats: "(7) | (7.5)" (regular | overtime hours)
- Real-time data validation and display

### 🔄 Flow Definition
- Create custom automation sequences with multiple event types:
  - **Click**: Click on HTML elements using selectors
  - **Input**: Enter text into form fields with placeholder support
  - **Wait**: Add delays between actions
  - **Scroll**: Navigate to specific page sections
  - **Select**: Choose options from dropdown menus
- Element validation with visual highlighting
- Save and load different flow configurations

### ⚡ Execution Control
- Real-time execution with progress tracking
- Pause/resume functionality
- Stop execution at any time
- Comprehensive logging and error handling

## Installation

### Method 1: Load Unpacked Extension (Development)

1. **Download the Extension**
   - Clone or download this repository to your local machine

2. **Open Chrome Extensions Page**
   - Open Google Chrome
   - Navigate to `chrome://extensions/`
   - Enable "Developer mode" (toggle in top-right corner)

3. **Load the Extension**
   - Click "Load unpacked"
   - Select the extension folder containing `manifest.json`
   - The extension should now appear in your extensions list

4. **Pin the Extension**
   - Click the puzzle piece icon in Chrome toolbar
   - Find "Venus Auto Fill" and click the pin icon

### Method 2: Chrome Web Store (Coming Soon)
The extension will be available on the Chrome Web Store after review and approval.

## Setup and Configuration

### 1. Google Apps Script Setup

Your Google Apps Script should return data in the following format:

```javascript
function doGet(e) {
  const action = e.parameter.action;
  const sheetName = e.parameter.sheet;
  
  if (action === 'test') {
    return ContentService
      .createTextOutput(JSON.stringify({ success: true, message: 'Connection successful' }))
      .setMimeType(ContentService.MimeType.JSON);
  }
  
  if (action === 'getData') {
    const sheet = SpreadsheetApp.getActiveSpreadsheet().getSheetByName(sheetName);
    const data = sheet.getDataRange().getValues();
    
    // Remove header row
    data.shift();
    
    return ContentService
      .createTextOutput(JSON.stringify({ success: true, data: data }))
      .setMimeType(ContentService.MimeType.JSON);
  }
  
  return ContentService
    .createTextOutput(JSON.stringify({ success: false, error: 'Invalid action' }))
    .setMimeType(ContentService.MimeType.JSON);
}
```

### 2. Extension Configuration

1. **Click the Venus Auto Fill icon** in your Chrome toolbar
2. **Go to Configuration tab**
3. **Enter your target website details**:
   - Target Website URL
   - Username and Password (stored securely)
4. **Verify Google Apps Script settings**:
   - Script URL (pre-configured)
   - Sheet Name (pre-configured)
5. **Click "Save Configuration"**
6. **Test the connection** using "Test Connection" button

## Usage Guide

### Step 1: Load Data
1. Switch to the **Data Preview** tab
2. Click **"Fetch Data"** to load attendance records
3. Review the data in the preview table
4. Verify data format and completeness

### Step 2: Define Automation Flow
1. Switch to the **Flow Definition** tab
2. Click **"Add Event"** to create automation steps
3. **Available event types**:
   - **Click**: `selector` - CSS selector for element to click
   - **Input**: `selector`, `value` - Input field and text to enter
   - **Wait**: `duration` - Delay in milliseconds
4. **Use placeholders in input values**:
   - `{employeeId}` - Employee ID from data
   - `{employeeName}` - Employee name from data
   - `{day1.regular}` - Regular hours for day 1
   - `{day1.overtime}` - Overtime hours for day 1
   - `{today}` - Current date (YYYY-MM-DD)
   - `{recordIndex}` - Current record index
5. **Test elements** using the "Check" button to highlight them on the page
6. **Save your flow** for reuse

### Step 3: Execute Automation
1. **Navigate to your target website** in the same tab
2. **Switch to Execution tab** in the extension
3. **Click "Run"** to start automation
4. **Monitor progress** with the progress bar and logs
5. **Use Pause/Stop** controls as needed

## Data Format

The extension expects attendance data in this format:

| Employee ID | Employee Name | Day 1 | Day 2 | ... | Day 31 |
|-------------|---------------|-------|-------|-----|--------|
| PTRJ.241000089 | Nursamsih | (7) \| (7.5) | (8) \| (0) | ... | (7) \| (1) |

Where each day's data follows the pattern: `(regular_hours) | (overtime_hours)`

## Advanced Features

### Element Selection Strategies
- **CSS Selectors**: `#id`, `.class`, `input[name="field"]`
- **XPath**: Use complex path expressions
- **Text Content**: Find elements by their text content
- **Attribute Matching**: Match elements by any attribute

### Placeholder System
Create dynamic flows using placeholders:
```
Input: #employee-id, Value: {employeeId}
Input: #hours-day-1, Value: {day1.regular}
Input: #overtime-day-1, Value: {day1.overtime}
```

### Error Handling
- Automatic retry for failed operations
- Detailed error logging
- Graceful degradation for missing elements
- User-friendly error messages

## Troubleshooting

### Common Issues

1. **"Element not found" errors**
   - Verify CSS selectors using browser developer tools
   - Use the "Check" button to test element selection
   - Ensure the page is fully loaded before execution

2. **Connection failed to Google Apps Script**
   - Check script URL and permissions
   - Verify script deployment settings
   - Ensure CORS is properly configured

3. **Data not loading**
   - Verify sheet name matches exactly
   - Check data format in Google Sheets
   - Ensure script has proper permissions

4. **Automation stops unexpectedly**
   - Check execution logs for error details
   - Verify target website hasn't changed structure
   - Ensure stable internet connection

### Debug Mode
Enable Chrome Developer Tools console to see detailed logs:
1. Press F12 to open Developer Tools
2. Go to Console tab
3. Look for Venus Auto Fill log messages

## Security and Privacy

- **Local Storage**: All configuration data is stored locally in Chrome
- **No Data Transmission**: Extension doesn't send data to external servers
- **Secure Credentials**: Passwords are stored using Chrome's secure storage API
- **Minimal Permissions**: Extension only requests necessary permissions

## Support and Contributing

### Reporting Issues
If you encounter any problems:
1. Check the troubleshooting section
2. Review console logs for error details
3. Create an issue with detailed reproduction steps

### Contributing
Contributions are welcome! Please:
1. Fork the repository
2. Create a feature branch
3. Submit a pull request with detailed description

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Version History

### v1.0.0 (Current)
- Initial release
- Basic automation features
- Google Apps Script integration
- Flow definition system
- Real-time execution control
