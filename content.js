// Chrome Extension Automation Bot - Content Script
class AutomationBotContent {
    constructor() {
        this.isExecuting = false;
        this.isPaused = false;
        this.currentExecution = null;
        this.automationData = null;
        this.flowEvents = [];
        this.extractedData = {};
        this.executionResults = {
            success: false,
            eventsExecuted: 0,
            errors: [],
            extractedData: {},
            startTime: null,
            endTime: null
        };
        
        this.init();
    }

    init() {
        console.log('Automation Bot Content Script initialized');
        this.setupMessageListener();
        this.injectAdvancedHelpers();
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep the message channel open for async response
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'executeAutomationFlow':
                    await this.executeAutomationFlow(message, sendResponse);
                    break;

                case 'pauseAutomation':
                    this.pauseAutomation();
                    sendResponse({ success: true });
                    break;

                case 'stopAutomation':
                    this.stopAutomation();
                    sendResponse({ success: true });
                    break;

                case 'testEvent':
                    await this.testSingleEvent(message.event, message.index, sendResponse);
                    break;

                case 'extractData':
                    await this.extractPageData(message.selectors, sendResponse);
                    break;

                case 'getPageInfo':
                    sendResponse(this.getPageInfo());
                    break;

                default:
                    console.warn('Unknown automation action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling automation message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeAutomationFlow(message, sendResponse) {
        if (this.isExecuting) {
            sendResponse({ success: false, error: 'Automation already running' });
            return;
        }

        console.log('🚀 Starting automation flow execution');
        
        this.isExecuting = true;
        this.isPaused = false;
        this.flowEvents = message.flowEvents || [];
        this.automationData = message.automationData || [];
        this.executionResults = {
            success: false,
            eventsExecuted: 0,
            errors: [],
            extractedData: {},
            startTime: new Date().toISOString(),
            endTime: null,
            executionId: message.metadata?.executionId || this.generateExecutionId()
        };

        try {
            // Send initial response
            sendResponse({ success: true, message: 'Automation flow started' });

            // Execute the flow
            await this.runFlowSequence();

            // Mark as successful if we got here
            this.executionResults.success = true;
            this.executionResults.endTime = new Date().toISOString();

            this.notifyCompletion(true, null, this.executionResults);

        } catch (error) {
            console.error('❌ Automation flow execution failed:', error);
            this.executionResults.success = false;
            this.executionResults.endTime = new Date().toISOString();
            this.executionResults.errors.push({
                message: error.message,
                timestamp: new Date().toISOString(),
                stack: error.stack
            });
            
            this.notifyCompletion(false, error.message, this.executionResults);
        } finally {
            this.isExecuting = false;
        }
    }

    async runFlowSequence() {
        console.log(`📋 Executing ${this.flowEvents.length} automation events`);
        
        for (let i = 0; i < this.flowEvents.length; i++) {
            if (!this.isExecuting || this.isPaused) {
                console.log('⏸️ Automation paused or stopped');
                break;
            }

            const event = this.flowEvents[i];
            console.log(`🎯 Executing event ${i + 1}/${this.flowEvents.length}: ${event.type}`);

            try {
                await this.executeEvent(event, i);
                this.executionResults.eventsExecuted++;

                // Update progress
                const progress = Math.round(((i + 1) / this.flowEvents.length) * 100);
                this.notifyProgress(progress, `Completed event ${i + 1}: ${event.type}`);

                // Add delay between events
                await this.delay(500 + Math.random() * 500);

            } catch (error) {
                console.error(`❌ Event ${i + 1} failed:`, error);
                this.executionResults.errors.push({
                    eventIndex: i,
                    eventType: event.type,
                    message: error.message,
                    timestamp: new Date().toISOString()
                });
                
                // Decide whether to continue or stop based on error type
                if (this.isCriticalError(error)) {
                    throw error;
                }
                
                // Continue with next event for non-critical errors
                console.log('⚠️ Non-critical error, continuing with next event');
            }
        }
    }

    async executeEvent(event, index) {
        console.log(`🔧 Executing ${event.type} event:`, event);

        switch (event.type) {
            case 'click':
                await this.executeClickEvent(event);
                break;
                
            case 'input':
                await this.executeInputEvent(event);
                break;
                
            case 'wait':
                await this.executeWaitEvent(event);
                break;
                
            case 'extract':
                await this.executeExtractEvent(event);
                break;
                
            case 'navigate':
                await this.executeNavigateEvent(event);
                break;
                
            case 'scroll':
                await this.executeScrollEvent(event);
                break;
                
            case 'condition':
                await this.executeConditionEvent(event, index);
                break;
                
            default:
                throw new Error(`Unknown event type: ${event.type}`);
        }

        // Notify about interaction
        this.notifyInteraction(event.type, event.selector || event.url || 'N/A', true);
    }

    async executeClickEvent(event) {
        const element = await this.findElement(event.selector, event.selectorType);

        if (!element) {
            // Try alternative selectors for login buttons
            const alternativeSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("LOG IN")',
                'button:contains("Login")',
                'button:contains("Sign In")',
                '[value*="LOG IN"]',
                '[value*="Login"]'
            ];

            let foundElement = null;
            for (const altSelector of alternativeSelectors) {
                foundElement = await this.findElement(altSelector, 'css');
                if (foundElement) break;
            }

            if (!foundElement) {
                throw new Error(`Click target not found: ${event.selector}`);
            }

            element = foundElement;
        }

        // Scroll to element if needed
        this.scrollElementIntoView(element);
        await this.delay(300);

        // Use advanced click simulation
        if (window.venusAutoFill && window.venusAutoFill.simulateHumanClick) {
            await window.venusAutoFill.simulateHumanClick(element);
        } else {
            // Fallback to simple click with multiple event types
            element.focus();
            element.click();

            // Dispatch additional events for better compatibility
            element.dispatchEvent(new MouseEvent('mousedown', { bubbles: true }));
            element.dispatchEvent(new MouseEvent('mouseup', { bubbles: true }));
            element.dispatchEvent(new MouseEvent('click', { bubbles: true }));
        }

        console.log(`✅ Clicked element: ${event.selector}`);
    }

    async executeInputEvent(event) {
        let element = await this.findElement(event.selector, event.selectorType);

        if (!element) {
            // Try alternative selectors based on the field type
            const isPasswordField = event.value === 'adm075' && event.selector.includes('password');
            const isUsernameField = event.value === 'adm075' && !event.selector.includes('password');

            let alternativeSelectors = [];

            if (isUsernameField) {
                alternativeSelectors = [
                    'input[type="text"]',
                    'input[name*="user"]',
                    'input[id*="user"]',
                    'input[placeholder*="user"]',
                    'input[name*="login"]',
                    'input[id*="login"]',
                    'input:not([type="password"]):not([type="hidden"]):not([type="submit"])'
                ];
            } else if (isPasswordField) {
                alternativeSelectors = [
                    'input[type="password"]',
                    'input[name*="pass"]',
                    'input[id*="pass"]',
                    'input[name*="pwd"]',
                    'input[id*="pwd"]'
                ];
            }

            for (const altSelector of alternativeSelectors) {
                element = await this.findElement(altSelector, 'css');
                if (element) {
                    console.log(`Found alternative element with selector: ${altSelector}`);
                    break;
                }
            }

            if (!element) {
                throw new Error(`Input target not found: ${event.selector}`);
            }
        }

        // Determine the value to input
        let inputValue = event.value;

        if (event.dataMapping && this.automationData) {
            inputValue = this.getDataValue(event.dataMapping) || event.value;
        }

        if (!inputValue) {
            throw new Error('No value specified for input event');
        }

        // Scroll to element and focus
        this.scrollElementIntoView(element);
        await this.delay(300);

        // Focus the element first
        element.focus();
        await this.delay(100);

        // Clear field if requested
        if (event.clearFirst !== false) {
            element.value = '';
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(100);
        }

        // Use advanced typing simulation
        if (window.venusAutoFill && window.venusAutoFill.simulateHumanType) {
            await window.venusAutoFill.simulateHumanType(element, inputValue, {
                clearFirst: event.clearFirst !== false
            });
        } else {
            // Fallback to simple input with better event simulation
            element.value = inputValue;

            // Dispatch multiple events for better compatibility
            element.dispatchEvent(new Event('input', { bubbles: true }));
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('keyup', { bubbles: true }));
        }

        console.log(`✅ Input value "${inputValue}" into: ${event.selector}`);
    }

    async executeWaitEvent(event) {
        const duration = event.duration || 1000;
        
        if (event.waitFor === 'element' && event.condition) {
            // Wait for element to appear
            console.log(`⏳ Waiting for element: ${event.condition}`);
            await this.waitForElement(event.condition, duration);
        } else if (event.waitFor === 'navigation') {
            // Wait for navigation to complete
            console.log(`⏳ Waiting for navigation (max ${duration}ms)`);
            await this.waitForNavigation(duration);
        } else {
            // Simple time delay
            console.log(`⏳ Waiting ${duration}ms`);
            await this.delay(duration);
        }

        console.log(`✅ Wait completed`);
    }

    async executeExtractEvent(event) {
        const element = await this.findElement(event.selector, event.selectorType);
        
        if (!element) {
            throw new Error(`Extract target not found: ${event.selector}`);
        }

        let extractedValue;
        
        switch (event.attribute) {
            case 'text':
                extractedValue = element.textContent?.trim();
                break;
            case 'value':
                extractedValue = element.value;
                break;
            case 'href':
                extractedValue = element.href;
                break;
            case 'src':
                extractedValue = element.src;
                break;
            case 'innerHTML':
                extractedValue = element.innerHTML;
                break;
            default:
                extractedValue = element.getAttribute(event.attribute);
        }

        // Store extracted data
        if (event.variableName) {
            this.extractedData[event.variableName] = extractedValue;
            this.executionResults.extractedData[event.variableName] = extractedValue;
        }

        console.log(`✅ Extracted ${event.attribute} from ${event.selector}: "${extractedValue}"`);
        
        // Notify about data extraction
        this.notifyDataExtraction(1);
    }

    async executeNavigateEvent(event) {
        if (!event.url) {
            throw new Error('Navigate event requires URL');
        }

        console.log(`🌐 Navigating to: ${event.url}`);
        
        window.location.href = event.url;
        
        if (event.waitForLoad !== false) {
            await this.waitForNavigation(event.timeout || 30000);
        }

        console.log(`✅ Navigation completed`);
    }

    async executeScrollEvent(event) {
        const distance = event.distance || 500;
        const direction = event.direction || 'down';
        
        let scrollOptions = { behavior: 'smooth' };
        
        if (event.target) {
            // Scroll to specific element
            const targetElement = await this.findElement(event.target);
            if (targetElement) {
                targetElement.scrollIntoView(scrollOptions);
            }
        } else {
            // Scroll page
            let scrollX = 0, scrollY = 0;
            
            switch (direction) {
                case 'down':
                    scrollY = distance;
                    break;
                case 'up':
                    scrollY = -distance;
                    break;
                case 'right':
                    scrollX = distance;
                    break;
                case 'left':
                    scrollX = -distance;
                    break;
            }
            
            window.scrollBy({ left: scrollX, top: scrollY, ...scrollOptions });
        }

        await this.delay(1000); // Wait for scroll to complete
        console.log(`✅ Scrolled ${direction} ${distance}px`);
    }

    async executeConditionEvent(event, currentIndex) {
        // Basic condition evaluation
        const conditionResult = await this.evaluateCondition(event.condition);
        
        console.log(`🔍 Condition "${event.condition}" evaluated to: ${conditionResult}`);
        
        // This is a simplified implementation
        // In a full implementation, you'd handle jumping to different events
        if (conditionResult && event.trueAction) {
            console.log(`✅ Condition true, would execute: ${event.trueAction}`);
        } else if (!conditionResult && event.falseAction) {
            console.log(`❌ Condition false, would execute: ${event.falseAction}`);
        }
    }

    async findElement(selector, selectorType = 'css', timeout = 10000) {
        if (!selector) {
            throw new Error('Selector is required');
        }

        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            let element;
            
            try {
                switch (selectorType) {
                    case 'xpath':
                        const result = document.evaluate(
                            selector,
                            document,
                            null,
                            XPathResult.FIRST_ORDERED_NODE_TYPE,
                            null
                        );
                        element = result.singleNodeValue;
                        break;
                        
                    case 'text':
                        if (window.venusAutoFill && window.venusAutoFill.findElementByText) {
                            element = window.venusAutoFill.findElementByText(selector);
                        } else {
                            // Fallback text search
                            element = Array.from(document.querySelectorAll('*'))
                                .find(el => el.textContent?.includes(selector));
                        }
                        break;
                        
                    default: // css
                        element = document.querySelector(selector);
                }
                
                if (element && this.isElementVisible(element)) {
                    return element;
                }
            } catch (error) {
                console.warn(`Error finding element with ${selectorType} selector "${selector}":`, error);
            }
            
            await this.delay(100);
        }
        
        return null;
    }

    isElementVisible(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        const style = window.getComputedStyle(element);
        
        return rect.width > 0 && 
               rect.height > 0 && 
               style.visibility !== 'hidden' && 
               style.display !== 'none' &&
               style.opacity !== '0';
    }

    scrollElementIntoView(element) {
        if (element && typeof element.scrollIntoView === 'function') {
            element.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center'
            });
        }
    }

    getDataValue(path) {
        // Extract value from automation data using dot notation
        // e.g., "user.email" -> automationData.user.email
        if (!this.automationData || !Array.isArray(this.automationData) || this.automationData.length === 0) {
            return null;
        }

        const pathParts = path.split('.');
        let value = this.automationData[0]; // Use first record for simplicity
        
        for (const part of pathParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            } else {
                return null;
            }
        }
        
        return value;
    }

    async waitForElement(selector, timeout = 10000) {
        const element = await this.findElement(selector, 'css', timeout);
        if (!element) {
            throw new Error(`Element not found within ${timeout}ms: ${selector}`);
        }
        return element;
    }

    async waitForNavigation(timeout = 30000) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkReady = () => {
                if (document.readyState === 'complete' || Date.now() - startTime > timeout) {
                    resolve();
                } else {
                    setTimeout(checkReady, 100);
                }
            };
            
            checkReady();
        });
    }

    async evaluateCondition(condition) {
        // Simple condition evaluation
        // In a full implementation, this would be much more sophisticated
        try {
            if (condition.includes('element.exists(')) {
                const selectorMatch = condition.match(/element\.exists\(['"`]([^'"`]+)['"`]\)/);
                if (selectorMatch) {
                    const selector = selectorMatch[1];
                    const element = document.querySelector(selector);
                    return !!element;
                }
            }
            
            // Add more condition types as needed
            return false;
        } catch (error) {
            console.error('Error evaluating condition:', error);
            return false;
        }
    }

    isCriticalError(error) {
        const criticalPatterns = [
            'Navigation failed',
            'Page not loaded',
            'Authentication required'
        ];
        
        return criticalPatterns.some(pattern => error.message.includes(pattern));
    }

    pauseAutomation() {
        this.isPaused = true;
        console.log('⏸️ Automation paused');
    }

    stopAutomation() {
        this.isExecuting = false;
        this.isPaused = false;
        console.log('⏹️ Automation stopped');
    }

    async testSingleEvent(event, index, sendResponse) {
        try {
            console.log(`🧪 Testing event: ${event.type}`);
            
            // Create a test environment
            const originalData = this.automationData;
            const originalExecuting = this.isExecuting;
            
            this.isExecuting = true;
            
            await this.executeEvent(event, index);
            
            // Restore state
            this.automationData = originalData;
            this.isExecuting = originalExecuting;
            
            sendResponse({ success: true, message: 'Event test passed' });
        } catch (error) {
            console.error('Event test failed:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async extractPageData(selectors, sendResponse) {
        const results = {};
        
        for (const [key, selector] of Object.entries(selectors)) {
            try {
                const element = document.querySelector(selector);
                if (element) {
                    results[key] = element.textContent?.trim() || element.value || '';
                }
            } catch (error) {
                console.error(`Failed to extract data for ${key}:`, error);
                results[key] = null;
            }
        }
        
        sendResponse({ success: true, data: results });
    }

    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
            timestamp: new Date().toISOString()
        };
    }

    generateExecutionId() {
        return 'exec_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11);
    }

    notifyProgress(progress, step) {
        chrome.runtime.sendMessage({
            action: 'executionProgress',
            progress: progress,
            step: step
        });
    }

    notifyCompletion(success, error = null, results = null) {
        chrome.runtime.sendMessage({
            action: 'executionComplete',
            success: success,
            error: error,
            results: results
        });
    }

    notifyInteraction(type, selector, success) {
        chrome.runtime.sendMessage({
            action: 'elementInteraction',
            type: type,
            selector: selector,
            success: success,
            status: success ? 'completed' : 'failed'
        });
    }

    notifyDataExtraction(dataPoints) {
        chrome.runtime.sendMessage({
            action: 'dataExtracted',
            dataPoints: dataPoints
        });
    }

    injectAdvancedHelpers() {
        // Inject the advanced automation helpers if not already present
        if (!window.venusAutoFillInjected) {
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('injected.js');
            script.onload = () => {
                console.log('✅ Advanced automation helpers injected');
            };
            (document.head || document.documentElement).appendChild(script);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

// Initialize the automation bot content script
const automationBotContent = new AutomationBotContent();
