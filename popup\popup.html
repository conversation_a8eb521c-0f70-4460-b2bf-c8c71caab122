<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto Form Fill Pro</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="header-content">
                <img src="../icons/icon-32.png" alt="Auto Form Fill Pro" class="logo">
                <div class="header-text">
                    <h1>Auto Form Fill Pro</h1>
                    <div class="status-indicator" id="statusIndicator">
                        <span class="status-dot" id="statusDot"></span>
                        <span class="status-text" id="statusText">Checking...</span>
                    </div>
                </div>
                <button class="settings-btn" id="settingsBtn" title="Open Settings">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V6a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V12a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                    </svg>
                </button>
            </div>
        </header>

        <!-- Main Controls -->
        <main class="main-content">
            <!-- Extension Toggle -->
            <section class="control-section">
                <div class="toggle-container">
                    <label class="toggle-switch">
                        <input type="checkbox" id="extensionToggle">
                        <span class="toggle-slider"></span>
                    </label>
                    <div class="toggle-info">
                        <span class="toggle-label">Auto-Fill Extension</span>
                        <span class="toggle-description" id="toggleDescription">Enable automatic form filling</span>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="actions-section">
                <button class="action-btn primary" id="fillFormsBtn" disabled>
                    <svg class="btn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h11a2 2 0 0 0 2-2v-7a2 2 0 0 0-2-2h-4"></path>
                        <polyline points="9,11 12,8 15,11"></polyline>
                        <line x1="12" y1="2" x2="12" y2="8"></line>
                    </svg>
                    <span class="btn-text" id="fillBtnText">Fill Forms</span>
                    <div class="btn-loader" id="fillBtnLoader">
                        <div class="loader-spinner"></div>
                    </div>
                </button>

                <button class="action-btn secondary" id="detectFormsBtn">
                    <svg class="btn-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                        <circle cx="12" cy="12" r="3"></circle>
                    </svg>
                    <span class="btn-text">Detect Forms</span>
                </button>
            </section>

            <!-- Current Page Info -->
            <section class="page-info-section">
                <div class="info-header">
                    <h3>Current Page</h3>
                    <button class="refresh-btn" id="refreshPageInfo" title="Refresh page information">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <polyline points="23 4 23 10 17 10"></polyline>
                            <polyline points="1 20 1 14 7 14"></polyline>
                            <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
                        </svg>
                    </button>
                </div>
                <div class="page-details">
                    <div class="detail-item">
                        <span class="detail-label">Domain:</span>
                        <span class="detail-value" id="currentDomain">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Forms Found:</span>
                        <span class="detail-value" id="formsCount">-</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Fields Found:</span>
                        <span class="detail-value" id="fieldsCount">-</span>
                    </div>
                </div>
            </section>

            <!-- API Status -->
            <section class="api-status-section">
                <div class="info-header">
                    <h3>API Status</h3>
                    <button class="test-btn" id="testApiBtn" title="Test API connection">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                    </button>
                </div>
                <div class="api-details">
                    <div class="detail-item">
                        <span class="detail-label">Connection:</span>
                        <span class="detail-value" id="apiConnection">Unknown</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Authentication:</span>
                        <span class="detail-value" id="apiAuth">Not checked</span>
                    </div>
                    <div class="detail-item">
                        <span class="detail-label">Last Response:</span>
                        <span class="detail-value" id="apiLastResponse">-</span>
                    </div>
                </div>
            </section>

            <!-- Quick Settings -->
            <section class="quick-settings-section">
                <div class="info-header">
                    <h3>Quick Settings</h3>
                </div>
                <div class="settings-grid">
                    <label class="setting-item">
                        <input type="checkbox" id="highlightFields">
                        <span class="setting-label">Highlight Fields</span>
                    </label>
                    <label class="setting-item">
                        <input type="checkbox" id="confirmBeforeFill">
                        <span class="setting-label">Confirm Before Fill</span>
                    </label>
                    <label class="setting-item">
                        <input type="checkbox" id="showNotifications">
                        <span class="setting-label">Show Notifications</span>
                    </label>
                    <label class="setting-item">
                        <input type="checkbox" id="smartDetection">
                        <span class="setting-label">Smart Detection</span>
                    </label>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <div class="shortcuts-info">
                <span class="shortcut-text">Press <kbd>Ctrl+Shift+F</kbd> to fill forms</span>
            </div>
            <div class="footer-links">
                <button class="link-btn" id="openOptions">Settings</button>
                <span class="separator">•</span>
                <button class="link-btn" id="showHelp">Help</button>
                <span class="separator">•</span>
                <button class="link-btn" id="showLogs">Logs</button>
            </div>
        </footer>

        <!-- Loading Overlay -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <span class="loading-text" id="loadingText">Loading...</span>
            </div>
        </div>

        <!-- Toast Notifications -->
        <div class="toast-container" id="toastContainer"></div>
    </div>

    <!-- Scripts -->
    <script src="popup.js"></script>
</body>
</html> 