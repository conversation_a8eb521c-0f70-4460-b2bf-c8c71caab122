{"manifest_version": 3, "name": "Chrome Extension Automation Bot", "version": "1.0.0", "description": "Powerful automation bot for web page interactions with local API integration", "permissions": ["storage", "activeTab", "scripting", "commands", "tabs"], "host_permissions": ["http://localhost:*/*", "https://localhost:*/*", "http://**********:*/*", "http://millwarep3.rebinmas.com:*/*", "*://*/*"], "background": {"service_worker": "background.js", "type": "module"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_idle"}], "action": {"default_popup": "popup.html", "default_title": "Automation Bot", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "commands": {"trigger-automation": {"suggested_key": {"default": "Ctrl+Shift+A", "mac": "Command+Shift+A"}, "description": "Trigger automation flow on current page"}, "toggle-extension": {"suggested_key": {"default": "Ctrl+Shift+T", "mac": "Command+Shift+T"}, "description": "Toggle automation bot on/off"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; frame-ancestors 'none';"}, "web_accessible_resources": [{"resources": ["injected.js", "icons/*.png"], "matches": ["<all_urls>"]}]}