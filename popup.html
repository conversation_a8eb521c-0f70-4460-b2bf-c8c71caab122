<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Venus Auto Fill</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="icons/icon32.png" alt="Venus Auto Fill" class="logo">
            <h1>Venus Auto Fill</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Ready</span>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="tab-button active" data-tab="config">Configuration</button>
            <button class="tab-button" data-tab="data">Data Preview</button>
            <button class="tab-button" data-tab="flow">Flow Definition</button>
            <button class="tab-button" data-tab="execution">Execution</button>
        </div>

        <!-- Configuration Tab -->
        <div class="tab-content active" id="config">
            <div class="section">
                <h3>Target Website Configuration</h3>
                <div class="form-group">
                    <label for="targetUrl">Target Website URL:</label>
                    <input type="url" id="targetUrl" placeholder="https://example.com">
                </div>
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" placeholder="Enter username">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" placeholder="Enter password">
                </div>
                <button class="btn btn-primary" id="saveConfig">Save Configuration</button>
            </div>

            <div class="section">
                <h3>Google Apps Script Configuration</h3>
                <div class="form-group">
                    <label for="scriptUrl">Apps Script URL:</label>
                    <input type="url" id="scriptUrl" 
                           value="https://script.google.com/macros/s/AKfycbzfhUsf1nHUy3ivBdjK-GkzTjcnM6oSUHKDRUDf9r0zKvvEeQcLl7y85nmXctZqRM7rDg/exec"
                           readonly>
                </div>
                <div class="form-group">
                    <label for="sheetName">Sheet Name:</label>
                    <input type="text" id="sheetName" value="monthlyGridData_May_2025" readonly>
                </div>
                <button class="btn btn-secondary" id="testConnection">Test Connection</button>
                <button class="btn btn-secondary" id="debugConnection">Debug Connection</button>
            </div>

            <div class="section">
                <h3>Debug Information</h3>
                <div class="debug-info" id="debugInfo" style="display: none;">
                    <div class="form-group">
                        <label>Current Configuration:</label>
                        <textarea id="debugOutput" readonly style="width: 100%; height: 120px; font-family: monospace; font-size: 11px; background: #f8f9fa; border: 1px solid #ddd; padding: 8px;"></textarea>
                    </div>
                    <button class="btn btn-secondary" id="copyDebugInfo">Copy Debug Info</button>
                </div>
            </div>
        </div>

        <!-- Data Preview Tab -->
        <div class="tab-content" id="data">
            <div class="section">
                <h3>Data Preview</h3>
                <div class="data-controls">
                    <button class="btn btn-primary" id="fetchData">Fetch Data</button>
                    <button class="btn btn-secondary" id="refreshData">Refresh</button>
                </div>
                <div class="data-status" id="dataStatus">
                    <span>No data loaded</span>
                </div>
                <div class="data-preview" id="dataPreview">
                    <!-- Data will be populated here -->
                </div>
            </div>
        </div>

        <!-- Flow Definition Tab -->
        <div class="tab-content" id="flow">
            <div class="section">
                <h3>Event Flow Definition</h3>
                <div class="flow-controls">
                    <button class="btn btn-primary" id="addEvent">Add Event</button>
                    <button class="btn btn-secondary" id="saveFlow">Save Flow</button>
                    <button class="btn btn-secondary" id="loadFlow">Load Flow</button>
                    <button class="btn btn-success" id="startFlow">Start Flow</button>
                </div>
                <div class="flow-list" id="flowList">
                    <!-- Flow events will be listed here -->
                </div>
            </div>
        </div>

        <!-- Execution Tab -->
        <div class="tab-content" id="execution">
            <div class="section">
                <h3>Execution Control</h3>
                <div class="execution-controls">
                    <button class="btn btn-success" id="runExecution">Run</button>
                    <button class="btn btn-warning" id="pauseExecution">Pause</button>
                    <button class="btn btn-danger" id="stopExecution">Stop</button>
                </div>
                <div class="execution-status" id="executionStatus">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="status-text" id="executionStatusText">Ready to execute</div>
                </div>
                <div class="execution-log" id="executionLog">
                    <!-- Execution logs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/popup.js"></script>
</body>
</html>
