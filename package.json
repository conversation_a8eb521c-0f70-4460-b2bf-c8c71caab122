{"name": "timesheet-automation-staging-api", "version": "1.0.0", "description": "Staging API server for Chrome Extension Automation Bot timesheet testing", "main": "staging-api-server.js", "scripts": {"start": "node staging-api-server.js", "dev": "nodemon staging-api-server.js", "test": "echo \"No tests specified\" && exit 0"}, "keywords": ["chrome-extension", "automation", "timesheet", "staging-api", "testing"], "author": "Chrome Extension Bot", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}