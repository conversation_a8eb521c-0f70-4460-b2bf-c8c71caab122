/**
 * Auto Form Fill Pro - Popup Interface Logic
 * Handles user interactions and UI state management
 */

class PopupInterface {
  constructor() {
    this.isEnabled = false;
    this.currentTab = null;
    this.extensionStatus = null;
    this.apiStatus = null;
    
    this.init();
  }

  /**
   * Initialize the popup interface
   */
  async init() {
    try {
      // Get current tab
      await this.getCurrentTab();
      
      // Set up event listeners
      this.setupEventListeners();
      
      // Load initial data
      await this.loadInitialData();
      
      // Update UI
      this.updateUI();
      
    } catch (error) {
      console.error('Failed to initialize popup:', error);
      this.showToast('Failed to initialize extension', 'error');
    }
  }

  /**
   * Get current active tab
   */
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  /**
   * Set up all event listeners
   */
  setupEventListeners() {
    // Extension toggle
    const extensionToggle = document.getElementById('extensionToggle');
    extensionToggle?.addEventListener('change', this.handleExtensionToggle.bind(this));

    // Action buttons
    const fillFormsBtn = document.getElementById('fillFormsBtn');
    fillFormsBtn?.addEventListener('click', this.handleFillForms.bind(this));

    const detectFormsBtn = document.getElementById('detectFormsBtn');
    detectFormsBtn?.addEventListener('click', this.handleDetectForms.bind(this));

    // Page info refresh
    const refreshPageInfo = document.getElementById('refreshPageInfo');
    refreshPageInfo?.addEventListener('click', this.handleRefreshPageInfo.bind(this));

    // API test
    const testApiBtn = document.getElementById('testApiBtn');
    testApiBtn?.addEventListener('click', this.handleTestApi.bind(this));

    // Quick settings
    this.setupQuickSettings();

    // Footer links
    const openOptions = document.getElementById('openOptions');
    openOptions?.addEventListener('click', this.handleOpenOptions.bind(this));

    const showHelp = document.getElementById('showHelp');
    showHelp?.addEventListener('click', this.handleShowHelp.bind(this));

    const showLogs = document.getElementById('showLogs');
    showLogs?.addEventListener('click', this.handleShowLogs.bind(this));

    // Settings button
    const settingsBtn = document.getElementById('settingsBtn');
    settingsBtn?.addEventListener('click', this.handleOpenOptions.bind(this));
  }

  /**
   * Set up quick settings event listeners
   */
  setupQuickSettings() {
    const settings = [
      'highlightFields',
      'confirmBeforeFill',
      'showNotifications',
      'smartDetection'
    ];

    settings.forEach(settingId => {
      const element = document.getElementById(settingId);
      element?.addEventListener('change', (e) => {
        this.handleQuickSettingChange(settingId, e.target.checked);
      });
    });
  }

  /**
   * Load initial data from background script
   */
  async loadInitialData() {
    try {
      // Get extension status
      const statusResponse = await this.sendMessage({ action: 'GET_EXTENSION_STATUS' });
      if (statusResponse.success) {
        this.extensionStatus = statusResponse.data;
        this.isEnabled = statusResponse.data.isEnabled;
      }

      // Get configuration
      const configResponse = await this.sendMessage({ action: 'GET_CONFIGURATION' });
      if (configResponse.success) {
        this.configuration = configResponse.data;
      }

      // Test API connection
      await this.testApiConnection();

      // Get page information
      await this.refreshPageInfo();

    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  }

  /**
   * Handle extension toggle
   */
  async handleExtensionToggle(event) {
    const isEnabled = event.target.checked;
    
    try {
      this.showLoading('Updating extension status...');
      
      const response = await this.sendMessage({ action: 'TOGGLE_EXTENSION' });
      
      if (response.success) {
        this.isEnabled = response.data.isEnabled;
        this.updateToggleState();
        this.showToast(
          `Extension ${this.isEnabled ? 'enabled' : 'disabled'}`,
          'success'
        );
      } else {
        throw new Error(response.error || 'Failed to toggle extension');
      }
    } catch (error) {
      console.error('Toggle error:', error);
      this.showToast('Failed to toggle extension', 'error');
      // Revert toggle state
      event.target.checked = this.isEnabled;
    } finally {
      this.hideLoading();
    }
  }

  /**
   * Handle fill forms action
   */
  async handleFillForms(event) {
    if (!this.isEnabled) {
      this.showToast('Extension is disabled', 'warning');
      return;
    }

    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);
      
      const response = await this.sendMessage({ 
        action: 'TRIGGER_AUTOFILL',
        data: { tabId: this.currentTab.id }
      });

      if (response.success) {
        this.showToast('Forms filled successfully', 'success');
        await this.refreshPageInfo();
      } else {
        throw new Error(response.error || 'Failed to fill forms');
      }
    } catch (error) {
      console.error('Fill forms error:', error);
      this.showToast('Failed to fill forms', 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
    }
  }

  /**
   * Handle detect forms action
   */
  async handleDetectForms(event) {
    try {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, true);

      // Send message to content script to detect forms
      const response = await chrome.tabs.sendMessage(this.currentTab.id, {
        action: 'DETECT_FORMS'
      });

      if (response?.success) {
        const { forms, fields } = response.data;
        this.updatePageInfo({
          formsCount: forms || 0,
          fieldsCount: fields || 0
        });
        this.showToast(`Found ${forms} forms with ${fields} fields`, 'success');
      } else {
        this.showToast('No forms detected on this page', 'warning');
      }
    } catch (error) {
      console.error('Detect forms error:', error);
      this.showToast('Failed to detect forms', 'error');
    } finally {
      const button = event.target.closest('.action-btn');
      this.setButtonLoading(button, false);
    }
  }

  /**
   * Handle refresh page info
   */
  async handleRefreshPageInfo() {
    try {
      await this.refreshPageInfo();
      this.showToast('Page information refreshed', 'success');
    } catch (error) {
      console.error('Refresh error:', error);
      this.showToast('Failed to refresh page info', 'error');
    }
  }

  /**
   * Handle API test
   */
  async handleTestApi() {
    try {
      await this.testApiConnection();
      this.showToast('API connection tested', 'success');
    } catch (error) {
      console.error('API test error:', error);
      this.showToast('API test failed', 'error');
    }
  }

  /**
   * Handle quick setting changes
   */
  async handleQuickSettingChange(settingId, value) {
    try {
      const settingMapping = {
        highlightFields: 'autoFill.highlightFields',
        confirmBeforeFill: 'autoFill.confirmBeforeFill',
        showNotifications: 'ui.showNotifications',
        smartDetection: 'autoFill.smartFieldDetection'
      };

      const configPath = settingMapping[settingId];
      if (!configPath) return;

      const [section, key] = configPath.split('.');
      const updates = { [section]: { [key]: value } };

      const response = await this.sendMessage({
        action: 'UPDATE_CONFIGURATION',
        data: updates
      });

      if (response.success) {
        this.showToast('Setting updated', 'success');
      } else {
        throw new Error(response.error || 'Failed to update setting');
      }
    } catch (error) {
      console.error('Setting update error:', error);
      this.showToast('Failed to update setting', 'error');
    }
  }

  /**
   * Handle open options page
   */
  handleOpenOptions() {
    chrome.runtime.openOptionsPage();
  }

  /**
   * Handle show help
   */
  handleShowHelp() {
    const helpUrl = chrome.runtime.getURL('help/help.html');
    chrome.tabs.create({ url: helpUrl });
  }

  /**
   * Handle show logs
   */
  async handleShowLogs() {
    try {
      // For now, just show a simple logs dialog
      // In a full implementation, you might open a dedicated logs page
      this.showToast('Logs feature coming soon', 'info');
    } catch (error) {
      console.error('Show logs error:', error);
    }
  }

  /**
   * Test API connection
   */
  async testApiConnection() {
    try {
      const response = await this.sendMessage({ action: 'TEST_API_CONNECTION' });
      
      if (response.success) {
        this.apiStatus = response.data;
        this.updateApiStatus();
      } else {
        this.apiStatus = { success: false, error: response.error };
        this.updateApiStatus();
      }
    } catch (error) {
      this.apiStatus = { success: false, error: error.message };
      this.updateApiStatus();
    }
  }

  /**
   * Refresh page information
   */
  async refreshPageInfo() {
    try {
      if (!this.currentTab) return;

      // Update domain info
      const domain = new URL(this.currentTab.url).hostname;
      this.updatePageInfo({ domain });

      // Try to get form information from content script
      try {
        const response = await chrome.tabs.sendMessage(this.currentTab.id, {
          action: 'GET_PAGE_STATS'
        });

        if (response?.success) {
          this.updatePageInfo(response.data);
        }
      } catch (error) {
        // Content script might not be injected yet
        this.updatePageInfo({ formsCount: 0, fieldsCount: 0 });
      }
    } catch (error) {
      console.error('Refresh page info error:', error);
    }
  }

  /**
   * Update UI components
   */
  updateUI() {
    this.updateToggleState();
    this.updateStatusIndicator();
    this.updateActionButtons();
    this.updateQuickSettings();
  }

  /**
   * Update toggle state
   */
  updateToggleState() {
    const toggle = document.getElementById('extensionToggle');
    const description = document.getElementById('toggleDescription');
    
    if (toggle) {
      toggle.checked = this.isEnabled;
    }
    
    if (description) {
      description.textContent = this.isEnabled 
        ? 'Extension is active and ready to fill forms'
        : 'Extension is disabled';
    }
  }

  /**
   * Update status indicator
   */
  updateStatusIndicator() {
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    
    if (!statusDot || !statusText) return;

    if (this.isEnabled && this.apiStatus?.success) {
      statusDot.className = 'status-dot connected';
      statusText.textContent = 'Ready';
    } else if (!this.isEnabled) {
      statusDot.className = 'status-dot';
      statusText.textContent = 'Disabled';
    } else {
      statusDot.className = 'status-dot error';
      statusText.textContent = 'API Error';
    }
  }

  /**
   * Update action buttons
   */
  updateActionButtons() {
    const fillBtn = document.getElementById('fillFormsBtn');
    
    if (fillBtn) {
      fillBtn.disabled = !this.isEnabled || !this.apiStatus?.success;
    }
  }

  /**
   * Update quick settings
   */
  updateQuickSettings() {
    if (!this.configuration) return;

    const settings = {
      highlightFields: this.configuration.autoFill?.highlightFields,
      confirmBeforeFill: this.configuration.autoFill?.confirmBeforeFill,
      showNotifications: this.configuration.ui?.showNotifications,
      smartDetection: this.configuration.autoFill?.smartFieldDetection
    };

    Object.entries(settings).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element && typeof value === 'boolean') {
        element.checked = value;
      }
    });
  }

  /**
   * Update page information display
   */
  updatePageInfo(info) {
    const elements = {
      currentDomain: info.domain,
      formsCount: info.formsCount,
      fieldsCount: info.fieldsCount
    };

    Object.entries(elements).forEach(([id, value]) => {
      const element = document.getElementById(id);
      if (element && value !== undefined) {
        element.textContent = value;
      }
    });
  }

  /**
   * Update API status display
   */
  updateApiStatus() {
    const elements = {
      apiConnection: document.getElementById('apiConnection'),
      apiAuth: document.getElementById('apiAuth'),
      apiLastResponse: document.getElementById('apiLastResponse')
    };

    if (elements.apiConnection) {
      if (this.apiStatus?.success) {
        elements.apiConnection.textContent = 'Connected';
        elements.apiConnection.className = 'detail-value success';
      } else {
        elements.apiConnection.textContent = 'Failed';
        elements.apiConnection.className = 'detail-value error';
      }
    }

    if (elements.apiAuth) {
      elements.apiAuth.textContent = this.apiStatus?.success ? 'Valid' : 'Invalid';
      elements.apiAuth.className = this.apiStatus?.success ? 'detail-value success' : 'detail-value error';
    }

    if (elements.apiLastResponse) {
      const timestamp = this.apiStatus?.timestamp;
      if (timestamp) {
        const time = new Date(timestamp).toLocaleTimeString();
        elements.apiLastResponse.textContent = time;
      }
    }
  }

  /**
   * Set button loading state
   */
  setButtonLoading(button, isLoading) {
    if (!button) return;

    if (isLoading) {
      button.classList.add('loading');
      button.disabled = true;
    } else {
      button.classList.remove('loading');
      button.disabled = false;
    }
  }

  /**
   * Show loading overlay
   */
  showLoading(text = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const loadingText = document.getElementById('loadingText');
    
    if (overlay) {
      overlay.classList.add('visible');
    }
    
    if (loadingText) {
      loadingText.textContent = text;
    }
  }

  /**
   * Hide loading overlay
   */
  hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
      overlay.classList.remove('visible');
    }
  }

  /**
   * Show toast notification
   */
  showToast(message, type = 'info', duration = 3000) {
    const container = document.getElementById('toastContainer');
    if (!container) return;

    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    toast.innerHTML = `
      <div class="toast-content">
        <div class="toast-text">${message}</div>
        <button class="toast-close">&times;</button>
      </div>
    `;

    // Add close functionality
    const closeBtn = toast.querySelector('.toast-close');
    closeBtn.addEventListener('click', () => {
      this.removeToast(toast);
    });

    container.appendChild(toast);

    // Show toast
    setTimeout(() => {
      toast.classList.add('visible');
    }, 10);

    // Auto-remove after duration
    setTimeout(() => {
      this.removeToast(toast);
    }, duration);
  }

  /**
   * Remove toast notification
   */
  removeToast(toast) {
    toast.classList.remove('visible');
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 300);
  }

  /**
   * Send message to background script
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, resolve);
    });
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new PopupInterface();
}); 