# 🤖 Chrome Extension Automation Bot

A powerful Chrome extension for automating web page interactions with local staging API integration, specifically designed for timesheet automation.

## ✨ **Features**

- ⚡ **One-Click Timesheet Automation**: Complete login and data processing automation
- 🎯 **Advanced Web Automation**: Click, input, scroll, extract data, and navigate pages
- 🔌 **Local API Integration**: Connect to your staging API at `localhost:5173`
- 🔐 **Secure Authentication**: Username/password authentication for target website
- 📊 **Real-time Monitoring**: Live execution progress and detailed logging
- 🛠️ **Visual Flow Builder**: Create automation sequences through intuitive interface
- 🧪 **Testing & Debugging**: Test individual events and debug connection issues
- 💾 **Flow Management**: Save, load, and reuse automation workflows

## 🚀 **Quick Start**

### **1. Installation**
1. Download or clone this repository
2. Open Chrome and navigate to `chrome://extensions/`
3. Enable "Developer mode" (toggle in top right)
4. Click "Load unpacked" and select the extension directory
5. The Automation Bot icon should appear in your extensions toolbar

### **2. Setup Staging API Server**
```bash
# Install dependencies
npm install

# Start the staging API server
npm start
```

The staging API will be available at `http://**********:5173/api`

### **3. Configure Extension**
1. Click the Automation Bot icon to open the popup
2. The extension comes pre-configured for timesheet automation:
   - **Target URL**: `http://millwarep3.rebinmas.com:8003/`
   - **Username**: `adm075`
   - **Password**: `adm075`
   - **API URL**: `http://**********:5173/api`
3. Click **Save Configuration** if you make any changes
4. Test your API connection with **Test Connection** button

### **4. Run Timesheet Automation**

#### **Option A: One-Click Automation (Recommended)**
1. Click the **⚡ Run Automation** button in the Quick Run section
2. The extension will automatically:
   - Fetch timesheet data from your staging API
   - Navigate to the target website
   - Fill in the login credentials
   - Submit the login form
   - Process the staging data

#### **Option B: Step-by-Step Automation**
1. Go to **Data Preview** tab and click **Fetch Staging Data**
2. Go to **Flow Definition** tab and click **Load Timesheet Flow**
3. Navigate to the target website manually
4. Go to **Execution** tab and click **Run Automation**

## 📋 **Timesheet Automation Flow**

The predefined automation sequence includes:

1. **Wait** for page to load (2 seconds)
2. **Input** username into text field (`adm075`)
3. **Wait** 1 second
4. **Input** password into password field (`adm075`)
5. **Wait** 1 second
6. **Click** LOGIN button
7. **Wait** for login completion (3 seconds)

## 🔧 **API Integration**

### **Staging API Endpoints**

The extension expects your staging API to provide these endpoints:

#### **Health Check**
```http
GET /api/health
Response: { "success": true, "data": { "name": "API", "status": "healthy" } }
```

#### **Timesheet Data**
```http
GET /api/staging/data
Response: { 
  "success": true, 
  "data": [
    {
      "employee_id": "EMP001",
      "employee_name": "John Doe",
      "date": "2024-01-15",
      "check_in": "08:00",
      "check_out": "17:30",
      "regular_hours": 8.0,
      "overtime_hours": 1.5,
      "task_code": "PROJ001"
    }
  ]
}
```

#### **Results Submission**
```http
POST /api/automation/results
Body: { "executionId": "...", "success": true, "results": {...} }
```

### **Sample Staging Data Structure**

```javascript
{
  employee_id: 'EMP001',
  employee_name: 'John Doe',
  date: '2024-01-15',
  check_in: '08:00',
  check_out: '17:30',
  regular_hours: 8.0,
  overtime_hours: 1.5,
  task_code: 'PROJ001',
  machine_code: 'MAC001',
  expense_code: 'EXP001'
}
```

## 🛠️ **Advanced Features**

### **Configurable Delays**
- Set custom delay intervals between automation steps (default: 1000ms)
- Fine-tune timing for different network conditions
- Prevents overwhelming target websites

### **Data Mapping**
Map API response data to form fields:
```javascript
// If API returns: { "employee": { "id": "EMP001" } }
// Use dataMapping: "employee.id" in input events
```

### **Flow Testing**
- Test individual events before running full automation
- Debug connection issues with built-in diagnostics
- View detailed execution logs in real-time

### **Error Handling**
- Automatic retry for failed elements
- Continue execution on non-critical errors
- Comprehensive error logging and reporting

## 🎯 **Use Cases**

- **Timesheet Automation**: Automatically login and process employee timesheet data
- **Form Automation**: Fill complex forms with staging API data
- **Data Entry**: Bulk data entry from your staging systems
- **Testing**: Automated UI testing workflows
- **Data Extraction**: Scrape data from web pages

## 🔍 **Troubleshooting**

### **API Connection Issues**
1. Ensure your staging API is running: `npm start`
2. Check if server is accessible at `http://localhost:5173`
3. Verify CORS settings allow chrome-extension origins
4. Use **Debug Connection** for detailed diagnostics

### **Login Automation Failures**
1. Check if target website is accessible
2. Verify credentials are correct (`adm075` / `adm075`)
3. Ensure login form elements are present on the page
4. Check execution logs for specific error messages

### **Data Processing Issues**
1. Verify staging data format matches expected structure
2. Check API endpoint returns valid JSON
3. Review timesheet data in **Data Preview** tab
4. Ensure all required fields are present

### **Permission Errors**
1. Ensure extension has necessary permissions
2. Check if target website blocks automation
3. Try reloading the extension
4. Verify manifest.json host permissions

## 📚 **Development Setup**

### **Prerequisites**
- Node.js 14+ installed
- Chrome browser with Developer mode enabled

### **Running the Staging API**
```bash
# Clone repository
git clone <repository-url>

# Install dependencies
npm install

# Start staging API server
npm start

# Server will be available at http://localhost:5173
```

### **Extension Development**
1. Load unpacked extension in Chrome
2. Make changes to code
3. Reload extension in chrome://extensions/
4. Test automation flow

## 📊 **Monitoring & Logs**

### **Real-time Progress**
- Live progress bar showing automation completion
- Step-by-step execution feedback
- Success/failure indicators for each action

### **Detailed Logging**
- Timestamp for each automation step
- Color-coded log levels (info, success, warning, error)
- Execution summary with statistics

### **Data Insights**
- Total timesheet records processed
- Regular vs overtime hours summary
- Employee count and processing status

## 🔒 **Security & Privacy**

- **Local Processing**: All data stays on your local machine
- **Secure Storage**: Credentials encrypted in browser storage
- **No External Calls**: Only communicates with your staging API
- **CORS Protected**: API server configured for extension access only

## 📝 **Configuration Options**

```javascript
// Default configuration
{
  apiBaseUrl: 'http://localhost:5173/api',
  targetUrl: 'http://millwarep3.rebinmas.com:8003/',
  username: 'adm075',
  password: 'adm075',
  delayInterval: 1000, // ms between steps
  retryAttempts: 3
}
```

## 🚀 **Future Enhancements**

- [ ] Scheduled automation runs
- [ ] Multiple target website support
- [ ] Advanced data transformation rules
- [ ] Custom webhook integrations
- [ ] Bulk processing capabilities
- [ ] Advanced error recovery

## 📞 **Support**

### **Quick Diagnostics**
1. Use **Debug Connection** feature for API diagnostics
2. Check **Execution** tab logs for automation details
3. Verify staging API server is running (`npm start`)
4. Test API endpoints manually: `curl http://localhost:5173/api/health`

### **Common Solutions**
- **Connection timeout**: Restart staging API server
- **Login fails**: Verify target website is accessible
- **No data**: Check API endpoint returns timesheet data
- **Permission denied**: Reload extension in Chrome

---

**Ready to automate your timesheet processing! 🎉**

### **Quick Test Commands**
```bash
# Test API health
curl http://localhost:5173/api/health

# Test staging data
curl http://localhost:5173/api/staging/data

# Start automation
1. Click "⚡ Run Automation" in extension popup
2. Watch real-time progress
3. Review execution logs
```
