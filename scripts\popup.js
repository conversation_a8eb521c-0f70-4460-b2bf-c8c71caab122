// Venus Auto Fill - Popup Script
class VenusAutoFillPopup {
    constructor() {
        this.currentTab = 'config';
        this.data = null;
        this.flowEvents = [];
        this.isExecuting = false;
        
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.loadSavedData();
        this.updateStatus('Ready');
        this.setupMessageListener();
    }

    setupEventListeners() {
        // Tab navigation
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Configuration tab
        document.getElementById('saveConfig').addEventListener('click', () => {
            this.saveConfiguration();
        });

        document.getElementById('testConnection').addEventListener('click', () => {
            this.testConnection();
        });

        document.getElementById('debugConnection').addEventListener('click', () => {
            this.debugConnection();
        });

        document.getElementById('copyDebugInfo').addEventListener('click', () => {
            this.copyDebugInfo();
        });

        // Data preview tab
        document.getElementById('fetchData').addEventListener('click', () => {
            this.fetchData();
        });

        document.getElementById('refreshData').addEventListener('click', () => {
            this.refreshData();
        });

        // Flow definition tab
        document.getElementById('addEvent').addEventListener('click', () => {
            this.addFlowEvent();
        });

        document.getElementById('saveFlow').addEventListener('click', () => {
            this.saveFlow();
        });

        document.getElementById('loadFlow').addEventListener('click', () => {
            this.loadFlow();
        });

        // New Start Flow button
        document.getElementById('startFlow').addEventListener('click', () => {
            this.startFlow();
        });

        // Execution tab
        document.getElementById('runExecution').addEventListener('click', () => {
            this.runExecution();
        });

        document.getElementById('pauseExecution').addEventListener('click', () => {
            this.pauseExecution();
        });

        document.getElementById('stopExecution').addEventListener('click', () => {
            this.stopExecution();
        });
    }

    setupMessageListener() {
        // Listen for messages from content script
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true;
        });
    }

    handleMessage(message, sender, sendResponse) {
        switch (message.action) {
            case 'executionProgress':
                this.updateExecutionStatus('Running...', message.progress);
                break;
            
            case 'executionComplete':
                this.isExecuting = false;
                if (message.success) {
                    this.updateExecutionStatus('Completed', 100);
                    this.updateStatus('Execution completed');
                    this.logExecution('Flow execution completed successfully', 'success');
                    this.showNotification('Automation completed successfully!', 'success');
                } else {
                    this.updateExecutionStatus('Failed', 0);
                    this.updateStatus('Execution failed');
                    this.logExecution('Flow execution failed: ' + (message.error || 'Unknown error'), 'error');
                    this.showNotification('Automation failed: ' + (message.error || 'Unknown error'), 'error');
                }
                break;
                
            default:
                // Handle other messages if needed
                break;
        }
        
        sendResponse({ success: true });
    }

    switchTab(tabName) {
        // Update tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');

        this.currentTab = tabName;
    }

    async saveConfiguration() {
        const config = {
            targetUrl: document.getElementById('targetUrl').value,
            username: document.getElementById('username').value,
            password: document.getElementById('password').value,
            scriptUrl: document.getElementById('scriptUrl').value,
            sheetName: document.getElementById('sheetName').value
        };

        try {
            await chrome.storage.local.set({ venusConfig: config });
            this.showNotification('Configuration saved successfully', 'success');
        } catch (error) {
            this.showNotification('Failed to save configuration', 'error');
            console.error('Save config error:', error);
        }
    }

    async loadSavedData() {
        try {
            const result = await chrome.storage.local.get(['venusConfig', 'venusFlowEvents']);
            
            if (result.venusConfig) {
                const config = result.venusConfig;
                document.getElementById('targetUrl').value = config.targetUrl || '';
                document.getElementById('username').value = config.username || '';
                document.getElementById('password').value = config.password || '';
                document.getElementById('scriptUrl').value = config.scriptUrl || '';
                document.getElementById('sheetName').value = config.sheetName || '';
            }

            if (result.venusFlowEvents) {
                this.flowEvents = result.venusFlowEvents;
                this.renderFlowEvents();
            }
        } catch (error) {
            console.error('Load saved data error:', error);
        }
    }

    async testConnection() {
        this.updateStatus('Testing connection...');
        
        try {
            const scriptUrl = document.getElementById('scriptUrl').value;
            const sheetName = document.getElementById('sheetName').value;
            
            // Enhanced validation
            if (!scriptUrl || !sheetName) {
                throw new Error('Script URL and Sheet Name are required');
            }
            
            // Validate URL format
            if (!scriptUrl.includes('script.google.com') || !scriptUrl.includes('/exec')) {
                throw new Error('Invalid Google Apps Script URL format. URL should end with /exec');
            }
            
            // Build test URL
            const testUrl = `${scriptUrl}?action=test&sheet=${encodeURIComponent(sheetName)}`;
            console.log('🔄 Testing connection to:', testUrl);
            
            // Create timeout controller
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                console.error('❌ Test connection timeout after 15 seconds');
            }, 15000);
            
            const response = await fetch(testUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                mode: 'cors',
                cache: 'no-cache',
                signal: controller.signal
            });
            
            // Clear timeout on successful response
            clearTimeout(timeoutId);
            
            console.log('📡 Test response received:', {
                status: response.status,
                statusText: response.statusText,
                url: response.url
            });
            
            if (!response.ok) {
                const errorText = await response.text().catch(() => 'Unable to read error response');
                throw new Error(`HTTP ${response.status}: ${response.statusText}\nResponse: ${errorText}`);
            }
            
            // Get and parse response
            const responseText = await response.text();
            console.log('📄 Test response text:', responseText);
            
            let result;
            try {
                result = JSON.parse(responseText);
                console.log('✅ Test response parsed:', result);
            } catch (parseError) {
                throw new Error(`Invalid JSON response.\nResponse: ${responseText.substring(0, 200)}...\nParse Error: ${parseError.message}`);
            }
            
            if (result.success === true) {
                const info = result.sheetInfo || {};
                this.showNotification(`Connection successful! Found sheet "${sheetName}" with ${info.rows || 'unknown'} rows.`, 'success');
                this.updateStatus('Connected');
                console.log('🎉 Connection test successful:', info);
            } else {
                const errorMsg = result.error || result.message || 'Unknown error from Google Apps Script';
                throw new Error(`Google Apps Script Test Error: ${errorMsg}`);
            }
            
        } catch (error) {
            console.error('❌ Connection test error:', error);
            
            // Enhanced error reporting for test connection
            let errorMessage = 'Connection error: ' + error.message;
            let debugInfo = '';
            
            if (error.name === 'AbortError') {
                errorMessage = 'Connection timeout - Google Apps Script not responding';
                debugInfo = 'Check if your Google Apps Script deployment is active';
            } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error - Cannot reach Google Apps Script';
                debugInfo = 'Verify your internet connection and script URL';
            } else if (error.message.includes('HTTP 404')) {
                errorMessage = 'Script not found (404) - Invalid deployment URL';
                debugInfo = 'Ensure you copied the correct web app URL from Google Apps Script';
            } else if (error.message.includes('HTTP 403')) {
                errorMessage = 'Access denied (403) - Permission issue';
                debugInfo = 'Set script execution to "Me" and access to "Anyone" in deployment settings';
            }
            
            this.showNotification(errorMessage, 'error');
            this.updateStatus('Connection failed');
            
            if (debugInfo) {
                console.log('💡 Debug tip:', debugInfo);
            }
            
            console.error('Test connection error:', error);
        }
    }

    async debugConnection() {
        const debugDiv = document.getElementById('debugInfo');
        const debugOutput = document.getElementById('debugOutput');
        
        // Show debug section
        debugDiv.style.display = 'block';
        
        // Collect debug information
        const scriptUrl = document.getElementById('scriptUrl').value;
        const sheetName = document.getElementById('sheetName').value;
        
        const debugInfo = {
            timestamp: new Date().toISOString(),
            configuration: {
                scriptUrl: scriptUrl,
                sheetName: sheetName,
                scriptUrlValid: scriptUrl.includes('script.google.com') && scriptUrl.includes('/exec'),
                urlLength: scriptUrl.length
            },
            browser: {
                userAgent: navigator.userAgent,
                language: navigator.language,
                online: navigator.onLine,
                cookieEnabled: navigator.cookieEnabled
            },
            extension: {
                manifestVersion: chrome.runtime.getManifest().version,
                permissions: chrome.runtime.getManifest().permissions,
                hostPermissions: chrome.runtime.getManifest().host_permissions
            },
            networkTest: null
        };
        
        // Display current debug info
        debugOutput.value = JSON.stringify(debugInfo, null, 2);
        
        // Perform network test
        try {
            this.updateStatus('Running debug tests...');
            
            const testUrl = `${scriptUrl}?action=test&sheet=${encodeURIComponent(sheetName)}`;
            const startTime = Date.now();
            
            const response = await fetch(testUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                mode: 'cors',
                cache: 'no-cache'
            });
            
            const endTime = Date.now();
            const responseText = await response.text();
            
            debugInfo.networkTest = {
                success: response.ok,
                status: response.status,
                statusText: response.statusText,
                responseTime: endTime - startTime,
                responseSize: responseText.length,
                headers: Object.fromEntries(response.headers.entries()),
                responsePreview: responseText.substring(0, 500),
                isJSON: false,
                parsedResponse: null
            };
            
            // Try to parse JSON
            try {
                const jsonResult = JSON.parse(responseText);
                debugInfo.networkTest.isJSON = true;
                debugInfo.networkTest.parsedResponse = jsonResult;
            } catch (e) {
                debugInfo.networkTest.jsonError = e.message;
            }
            
            this.updateStatus('Debug test completed');
            this.showNotification('Debug information collected successfully', 'success');
            
        } catch (error) {
            debugInfo.networkTest = {
                success: false,
                error: error.message,
                errorName: error.name,
                stack: error.stack
            };
            
            this.updateStatus('Debug test failed');
            this.showNotification('Debug test failed: ' + error.message, 'error');
        }
        
        // Update debug output with network test results
        debugOutput.value = JSON.stringify(debugInfo, null, 2);
        
        // Scroll to debug section
        debugDiv.scrollIntoView({ behavior: 'smooth' });
    }

    copyDebugInfo() {
        const debugOutput = document.getElementById('debugOutput');
        
        try {
            debugOutput.select();
            document.execCommand('copy');
            this.showNotification('Debug information copied to clipboard', 'success');
        } catch (error) {
            // Fallback for modern browsers
            navigator.clipboard.writeText(debugOutput.value).then(() => {
                this.showNotification('Debug information copied to clipboard', 'success');
            }).catch(() => {
                this.showNotification('Failed to copy debug information', 'error');
            });
        }
    }

    async fetchData() {
        this.updateStatus('Fetching data...');
        document.getElementById('dataStatus').innerHTML = '<span>Loading data...</span>';
        
        try {
            const scriptUrl = document.getElementById('scriptUrl').value;
            const sheetName = document.getElementById('sheetName').value;
            
            // Enhanced validation
            if (!scriptUrl || !sheetName) {
                throw new Error('Script URL and Sheet Name are required');
            }
            
            // Validate URL format
            if (!scriptUrl.includes('script.google.com') || !scriptUrl.includes('/exec')) {
                throw new Error('Invalid Google Apps Script URL format. URL should end with /exec');
            }
            
            // Build request URL with detailed logging
            const requestUrl = `${scriptUrl}?action=getData&sheet=${encodeURIComponent(sheetName)}`;
            console.log('🚀 Fetching data from:', requestUrl);
            
            // Log request details for debugging
            this.logExecution(`Starting data fetch from: ${scriptUrl}`, 'info');
            this.logExecution(`Sheet name: ${sheetName}`, 'info');
            
            // Create timeout controller with detailed error handling
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                console.error('❌ Request timeout after 30 seconds');
            }, 30000);
            
            // Enhanced fetch with detailed error tracking
            const response = await fetch(requestUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                mode: 'cors',
                cache: 'no-cache',
                signal: controller.signal
            });
            
            // Clear timeout on successful response
            clearTimeout(timeoutId);
            
            console.log('📡 Response received:', {
                status: response.status,
                statusText: response.statusText,
                headers: Object.fromEntries(response.headers.entries()),
                url: response.url
            });
            
            // Check response status
            if (!response.ok) {
                const errorText = await response.text().catch(() => 'Unable to read error response');
                throw new Error(`HTTP ${response.status}: ${response.statusText}\nResponse: ${errorText}`);
            }
            
            // Get response text first for debugging
            const responseText = await response.text();
            console.log('📄 Raw response text:', responseText);
            
            // Try to parse JSON
            let result;
            try {
                result = JSON.parse(responseText);
                console.log('✅ Parsed JSON successfully:', result);
            } catch (parseError) {
                console.error('❌ JSON parse error:', parseError);
                throw new Error(`Invalid JSON response from Google Apps Script.\nResponse: ${responseText.substring(0, 500)}...\nParse Error: ${parseError.message}`);
            }
            
            // Check if result has success property
            if (typeof result !== 'object' || result === null) {
                throw new Error(`Invalid response format. Expected object, got: ${typeof result}`);
            }
            
            if (result.success === true) {
                // Validate data structure
                if (!Array.isArray(result.data)) {
                    throw new Error(`Invalid data format. Expected array, got: ${typeof result.data}`);
                }
                
                console.log('🎉 Data fetch successful:', {
                    recordCount: result.data.length,
                    metadata: result.metadata
                });
                
                this.data = result.data;
                this.renderDataPreview();
                this.updateStatus('Data loaded');
                document.getElementById('dataStatus').innerHTML = `<span>Loaded ${this.data.length} records</span>`;
                this.showNotification(`Successfully loaded ${this.data.length} records`, 'success');
                this.logExecution(`Successfully loaded ${this.data.length} records`, 'success');
                
            } else {
                const errorMsg = result.error || result.message || 'Unknown error from Google Apps Script';
                throw new Error(`Google Apps Script Error: ${errorMsg}`);
            }
            
        } catch (error) {
            console.error('❌ Data fetch error:', error);
            
            // Enhanced error reporting
            let errorMessage = 'Data fetch error: ' + error.message;
            let debugInfo = '';
            
            if (error.name === 'AbortError') {
                errorMessage = 'Request timeout - Google Apps Script took too long to respond';
                debugInfo = 'Try checking your Google Apps Script deployment and internet connection';
            } else if (error.message.includes('CORS')) {
                errorMessage = 'CORS error - Google Apps Script access blocked';
                debugInfo = 'Ensure your Google Apps Script is deployed with "Anyone" access';
            } else if (error.message.includes('NetworkError') || error.message.includes('Failed to fetch')) {
                errorMessage = 'Network error - Unable to reach Google Apps Script';
                debugInfo = 'Check your internet connection and script URL';
            } else if (error.message.includes('HTTP 404')) {
                errorMessage = 'Google Apps Script not found (404)';
                debugInfo = 'Verify your script URL is correct and the deployment is active';
            } else if (error.message.includes('HTTP 403')) {
                errorMessage = 'Access denied to Google Apps Script (403)';
                debugInfo = 'Check script permissions - should be set to "Anyone" access';
            }
            
            this.showNotification(errorMessage, 'error');
            this.updateStatus('Data fetch error');
            document.getElementById('dataStatus').innerHTML = `<span style="color: #dc3545;">Failed to load data</span>`;
            this.logExecution(`Data fetch failed: ${errorMessage}`, 'error');
            
            if (debugInfo) {
                this.logExecution(`Debug tip: ${debugInfo}`, 'warning');
                console.log('💡 Debug tip:', debugInfo);
            }
            
            // Log additional debugging information
            const scriptUrl = document.getElementById('scriptUrl').value;
            const sheetName = document.getElementById('sheetName').value;
            console.log('🔍 Debug Information:', {
                scriptUrl,
                sheetName,
                error: error.message,
                stack: error.stack
            });
        }
    }

    refreshData() {
        this.fetchData();
    }

    renderDataPreview() {
        const previewContainer = document.getElementById('dataPreview');
        
        if (!this.data || this.data.length === 0) {
            previewContainer.innerHTML = '<p>No data available</p>';
            return;
        }

        // Create table
        const table = document.createElement('table');
        table.className = 'data-table';
        
        // Create header
        const thead = document.createElement('thead');
        const headerRow = document.createElement('tr');
        
        const headers = ['Employee ID', 'Employee Name', 'Attendance Data'];
        headers.forEach(header => {
            const th = document.createElement('th');
            th.textContent = header;
            headerRow.appendChild(th);
        });
        
        thead.appendChild(headerRow);
        table.appendChild(thead);
        
        // Create body
        const tbody = document.createElement('tbody');
        
        this.data.slice(0, 10).forEach(row => { // Show first 10 rows
            const tr = document.createElement('tr');
            
            const idCell = document.createElement('td');
            idCell.textContent = row.employeeId || 'N/A';
            tr.appendChild(idCell);
            
            const nameCell = document.createElement('td');
            nameCell.textContent = row.employeeName || 'N/A';
            tr.appendChild(nameCell);
            
            const dataCell = document.createElement('td');
            dataCell.textContent = row.attendanceData || 'N/A';
            tr.appendChild(dataCell);
            
            tbody.appendChild(tr);
        });
        
        table.appendChild(tbody);
        previewContainer.innerHTML = '';
        previewContainer.appendChild(table);
        
        if (this.data.length > 10) {
            const moreInfo = document.createElement('p');
            moreInfo.textContent = `... and ${this.data.length - 10} more records`;
            moreInfo.style.textAlign = 'center';
            moreInfo.style.color = '#6c757d';
            moreInfo.style.fontSize = '12px';
            moreInfo.style.marginTop = '10px';
            previewContainer.appendChild(moreInfo);
        }
    }

    addFlowEvent() {
        // This will open a modal or form to add new flow events
        // For now, we'll add a simple example
        const eventType = prompt('Event type (click, input, wait):');
        if (!eventType) return;
        
        let eventData = {};
        
        switch (eventType.toLowerCase()) {
            case 'click':
                eventData.selector = prompt('Element selector:');
                break;
            case 'input':
                eventData.selector = prompt('Input element selector:');
                eventData.value = prompt('Value to input:');
                break;
            case 'wait':
                eventData.duration = parseInt(prompt('Wait duration (ms):'));
                break;
            default:
                this.showNotification('Invalid event type', 'error');
                return;
        }
        
        const event = {
            id: Date.now(),
            type: eventType.toLowerCase(),
            ...eventData
        };
        
        this.flowEvents.push(event);
        this.renderFlowEvents();
        this.saveFlowEvents();
    }

    renderFlowEvents() {
        const flowList = document.getElementById('flowList');
        
        if (this.flowEvents.length === 0) {
            flowList.innerHTML = '<p style="padding: 20px; text-align: center; color: #6c757d;">No events defined</p>';
            return;
        }
        
        flowList.innerHTML = '';
        
        this.flowEvents.forEach((event, index) => {
            const eventDiv = document.createElement('div');
            eventDiv.className = 'flow-event';
            
            const infoDiv = document.createElement('div');
            infoDiv.className = 'event-info';
            
            const typeSpan = document.createElement('div');
            typeSpan.className = 'event-type';
            typeSpan.textContent = event.type.toUpperCase();
            
            const detailsSpan = document.createElement('div');
            detailsSpan.className = 'event-details';
            
            switch (event.type) {
                case 'click':
                    detailsSpan.textContent = `Click: ${event.selector}`;
                    break;
                case 'input':
                    detailsSpan.textContent = `Input: ${event.selector} = "${event.value}"`;
                    break;
                case 'wait':
                    detailsSpan.textContent = `Wait: ${event.duration}ms`;
                    break;
            }
            
            infoDiv.appendChild(typeSpan);
            infoDiv.appendChild(detailsSpan);
            
            const actionsDiv = document.createElement('div');
            actionsDiv.className = 'event-actions';
            
            const checkBtn = document.createElement('button');
            checkBtn.className = 'btn btn-secondary btn-small';
            checkBtn.textContent = 'Check';
            checkBtn.addEventListener('click', () => this.checkElement(event));
            
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-danger btn-small';
            deleteBtn.textContent = 'Delete';
            deleteBtn.addEventListener('click', () => this.deleteFlowEvent(index));
            
            actionsDiv.appendChild(checkBtn);
            actionsDiv.appendChild(deleteBtn);
            
            eventDiv.appendChild(infoDiv);
            eventDiv.appendChild(actionsDiv);
            
            flowList.appendChild(eventDiv);
        });
    }

    async checkElement(event) {
        if (event.type !== 'click' && event.type !== 'input') {
            this.showNotification('Element check only available for click and input events', 'warning');
            return;
        }
        
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            await chrome.scripting.executeScript({
                target: { tabId: tab.id },
                func: (selector) => {
                    const element = document.querySelector(selector);
                    if (element) {
                        // Highlight element
                        element.style.outline = '3px solid #ff0000';
                        element.style.outlineOffset = '2px';
                        
                        // Remove highlight after 3 seconds
                        setTimeout(() => {
                            element.style.outline = '';
                            element.style.outlineOffset = '';
                        }, 3000);
                        
                        return true;
                    }
                    return false;
                },
                args: [event.selector]
            });
            
            this.showNotification('Element highlighted on page', 'success');
        } catch (error) {
            this.showNotification('Failed to check element: ' + error.message, 'error');
            console.error('Check element error:', error);
        }
    }

    deleteFlowEvent(index) {
        this.flowEvents.splice(index, 1);
        this.renderFlowEvents();
        this.saveFlowEvents();
    }

    async saveFlowEvents() {
        try {
            await chrome.storage.local.set({ venusFlowEvents: this.flowEvents });
        } catch (error) {
            console.error('Save flow events error:', error);
        }
    }

    saveFlow() {
        const flowName = prompt('Enter flow name:');
        if (!flowName) return;
        
        // Save flow with name for later loading
        chrome.storage.local.get(['venusFlows'], (result) => {
            const flows = result.venusFlows || {};
            flows[flowName] = this.flowEvents;
            chrome.storage.local.set({ venusFlows: flows });
            this.showNotification(`Flow "${flowName}" saved`, 'success');
        });
    }

    loadFlow() {
        chrome.storage.local.get(['venusFlows'], (result) => {
            const flows = result.venusFlows || {};
            const flowNames = Object.keys(flows);
            
            if (flowNames.length === 0) {
                this.showNotification('No saved flows found', 'warning');
                return;
            }
            
            const flowName = prompt(`Available flows: ${flowNames.join(', ')}\n\nEnter flow name to load:`);
            if (!flowName || !flows[flowName]) {
                this.showNotification('Flow not found', 'error');
                return;
            }
            
            this.flowEvents = flows[flowName];
            this.renderFlowEvents();
            this.saveFlowEvents();
            this.showNotification(`Flow "${flowName}" loaded`, 'success');
        });
    }

    async startFlow() {
        if (this.isExecuting) {
            this.showNotification('Execution already in progress', 'warning');
            return;
        }
        
        if (this.flowEvents.length === 0) {
            this.showNotification('No flow events defined', 'error');
            return;
        }
        
        // Check if data is loaded
        if (!this.data) {
            this.showNotification('No data loaded. Please fetch data first.', 'error');
            return;
        }
        
        // Get target URL from configuration
        const targetUrl = document.getElementById('targetUrl').value;
        if (!targetUrl) {
            this.showNotification('Target URL not configured. Please set target URL in Configuration tab.', 'error');
            return;
        }
        
        this.isExecuting = true;
        this.updateStatus('Starting flow execution...');
        this.logExecution('Starting flow with auto-navigation...', 'info');
        
        try {
            // Step 1: Navigate to target URL
            this.logExecution(`Navigating to: ${targetUrl}`, 'info');
            
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Navigate to target URL
            await chrome.tabs.update(currentTab.id, { url: targetUrl });
            
            // Wait for page to load
            await this.waitForPageLoad(currentTab.id);
            
            this.logExecution('Page loaded successfully', 'success');
            
            // Step 2: Execute the flow
            this.logExecution('Starting flow execution...', 'info');
            
            await chrome.tabs.sendMessage(currentTab.id, {
                action: 'executeFlow',
                flowEvents: this.flowEvents,
                data: this.data
            });
            
        } catch (error) {
            this.showNotification('Flow execution failed: ' + error.message, 'error');
            this.logExecution('Flow execution failed: ' + error.message, 'error');
            this.isExecuting = false;
            this.updateStatus('Flow execution failed');
        }
    }

    async waitForPageLoad(tabId, timeout = 30000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkComplete = () => {
                chrome.tabs.get(tabId, (tab) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                        return;
                    }
                    
                    if (tab.status === 'complete') {
                        // Additional delay to ensure page is fully loaded
                        setTimeout(resolve, 2000);
                    } else if (Date.now() - startTime > timeout) {
                        reject(new Error('Page load timeout'));
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                });
            };
            
            checkComplete();
        });
    }

    async runExecution() {
        if (this.isExecuting) {
            this.showNotification('Execution already in progress', 'warning');
            return;
        }
        
        if (this.flowEvents.length === 0) {
            this.showNotification('No flow events defined', 'error');
            return;
        }
        
        if (!this.data) {
            this.showNotification('No data loaded', 'error');
            return;
        }
        
        // Get target URL from configuration
        const targetUrl = document.getElementById('targetUrl').value;
        if (!targetUrl) {
            this.showNotification('Target URL not configured. Please set target URL in Configuration tab.', 'error');
            return;
        }
        
        this.isExecuting = true;
        this.updateExecutionStatus('Running...', 0);
        this.logExecution('Execution started with auto-navigation', 'info');
        
        try {
            // Step 1: Navigate to target URL
            this.logExecution(`Navigating to: ${targetUrl}`, 'info');
            this.updateExecutionStatus('Navigating to target URL...', 5);
            
            const [currentTab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            // Navigate to target URL
            await chrome.tabs.update(currentTab.id, { url: targetUrl });
            
            // Wait for page to load
            await this.waitForPageLoad(currentTab.id);
            
            this.logExecution('Page loaded successfully', 'success');
            this.updateExecutionStatus('Page loaded, starting automation...', 10);
            
            // Step 2: Execute the flow
            await chrome.tabs.sendMessage(currentTab.id, {
                action: 'executeFlow',
                flowEvents: this.flowEvents,
                data: this.data
            });
            
        } catch (error) {
            this.showNotification('Execution failed: ' + error.message, 'error');
            this.logExecution('Execution failed: ' + error.message, 'error');
            this.isExecuting = false;
            this.updateExecutionStatus('Failed', 0);
        }
    }

    pauseExecution() {
        if (!this.isExecuting) return;
        
        // Send pause command to content script
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'pauseExecution' });
        });
        
        this.updateExecutionStatus('Paused', null);
        this.logExecution('Execution paused', 'warning');
    }

    stopExecution() {
        if (!this.isExecuting) return;
        
        // Send stop command to content script
        chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
            chrome.tabs.sendMessage(tabs[0].id, { action: 'stopExecution' });
        });
        
        this.isExecuting = false;
        this.updateExecutionStatus('Stopped', 0);
        this.logExecution('Execution stopped', 'error');
    }

    updateStatus(status) {
        document.querySelector('.status-text').textContent = status;
    }

    updateExecutionStatus(status, progress) {
        document.getElementById('executionStatusText').textContent = status;
        if (progress !== null) {
            document.getElementById('progressFill').style.width = progress + '%';
        }
    }

    logExecution(message, type = 'info') {
        const logContainer = document.getElementById('executionLog');
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry log-${type}`;
        
        const timestamp = new Date().toLocaleTimeString();
        logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span> ${message}`;
        
        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    showNotification(message, type = 'info') {
        // Simple notification system
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // You could implement a toast notification system here
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = statusIndicator.querySelector('.status-text');
        const originalText = statusText.textContent;
        
        statusText.textContent = message;
        
        setTimeout(() => {
            statusText.textContent = originalText;
        }, 3000);
    }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new VenusAutoFillPopup();
});
