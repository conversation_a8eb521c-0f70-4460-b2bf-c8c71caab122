<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome Extension Automation Bot</title>
    <link rel="stylesheet" href="styles/popup.css">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <img src="icons/icon32.png" alt="Automation Bot" class="logo">
            <h1>Automation Bot</h1>
            <div class="status-indicator" id="statusIndicator">
                <span class="status-dot"></span>
                <span class="status-text">Ready</span>
            </div>
        </div>

        <!-- Quick Run Section -->
        <div class="quick-run-section">
            <h3>🚀 Timesheet Automation</h3>
            <div class="quick-run-info">
                <div class="info-item">
                    <strong>Target:</strong> millwarep3.rebinmas.com:8003
                </div>
                <div class="info-item">
                    <strong>API:</strong> localhost:5173/api/staging/data
                </div>
            </div>
            <button class="btn btn-success btn-large btn-primary-action" id="runTimesheetAutomation" title="Run complete timesheet automation: login + data processing">
                ⚡ Run Automation
            </button>
            <small style="display: block; margin-top: 8px; color: #666; font-size: 12px;">
                Auto-login to timesheet system and process staging data
            </small>
        </div>

        <!-- Navigation Tabs -->
        <div class="nav-tabs">
            <button class="tab-button active" data-tab="config">Configuration</button>
            <button class="tab-button" data-tab="data">Data Preview</button>
            <button class="tab-button" data-tab="flow">Flow Definition</button>
            <button class="tab-button" data-tab="execution">Execution</button>
        </div>

        <!-- Configuration Tab -->
        <div class="tab-content active" id="config">
            <div class="section">
                <h3>Target Website Configuration</h3>
                <div class="form-group">
                    <label for="targetUrl">Target Website URL:</label>
                    <input type="url" id="targetUrl" 
                           value="http://millwarep3.rebinmas.com:8003/"
                           placeholder="http://millwarep3.rebinmas.com:8003/">
                </div>
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" 
                           value="adm075"
                           placeholder="adm075">
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" 
                           value="adm075"
                           placeholder="Enter password">
                </div>
                <button class="btn btn-primary" id="saveConfig">Save Configuration</button>
            </div>

            <div class="section">
                <h3>Staging API Configuration</h3>
                <div class="form-group">
                    <label for="scriptUrl">API Base URL:</label>
                    <input type="url" id="scriptUrl" 
                           value="http://localhost:5173/api"
                           placeholder="http://localhost:5173/api">
                </div>
                <div class="form-group">
                    <label for="sheetName">API Key (Optional):</label>
                    <input type="text" id="sheetName" placeholder="Enter API key if required">
                </div>
                <div class="form-group">
                    <label for="delayInterval">Step Delay (ms):</label>
                    <input type="number" id="delayInterval" 
                           value="1000" 
                           min="500" 
                           max="5000"
                           placeholder="1000">
                    <small>Delay between automation steps (default: 1000ms)</small>
                </div>
                <button class="btn btn-secondary" id="testConnection">Test Connection</button>
                <button class="btn btn-secondary" id="debugConnection">Debug Connection</button>
            </div>

            <div class="section">
                <h3>Debug Information</h3>
                <div class="debug-info" id="debugInfo" style="display: none;">
                    <div class="form-group">
                        <label>Current Configuration:</label>
                        <textarea id="debugOutput" readonly style="width: 100%; height: 120px; font-family: monospace; font-size: 11px; background: #f8f9fa; border: 1px solid #ddd; padding: 8px;"></textarea>
                    </div>
                    <button class="btn btn-secondary" id="copyDebugInfo">Copy Debug Info</button>
                </div>
            </div>
        </div>

        <!-- Data Preview Tab -->
        <div class="tab-content" id="data">
            <div class="section">
                <h3>Timesheet Data Preview</h3>
                <div class="data-controls">
                    <button class="btn btn-primary" id="fetchData">Fetch Staging Data</button>
                    <button class="btn btn-secondary" id="refreshData">Refresh</button>
                </div>
                <div class="data-status" id="dataStatus">
                    <span>No timesheet data loaded</span>
                </div>
                <div class="data-preview" id="dataPreview">
                    <!-- Data will be populated here -->
                </div>
            </div>
        </div>

        <!-- Flow Definition Tab -->
        <div class="tab-content" id="flow">
            <div class="section">
                <h3>Automation Flow Definition</h3>
                <div class="flow-controls">
                    <button class="btn btn-primary" id="addEvent">Add Event</button>
                    <button class="btn btn-secondary" id="saveFlow">Save Flow</button>
                    <button class="btn btn-secondary" id="loadFlow">Load Flow</button>
                    <button class="btn btn-warning" id="loadPredefinedFlow">Load Timesheet Flow</button>
                </div>
                <div class="flow-list" id="flowList">
                    <!-- Flow events will be listed here -->
                </div>
                <div class="flow-quick-actions" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                    <h4 style="margin-bottom: 10px; font-size: 14px; color: #333;">Quick Actions</h4>
                    <button class="btn btn-success btn-large" id="startFlow" title="Execute the defined automation flow immediately with auto-navigation">
                        🚀 Start Automation Flow
                    </button>
                    <small style="display: block; margin-top: 8px; color: #666; font-size: 12px;">
                        This will automatically navigate to your target URL and execute the defined automation sequence.
                    </small>
                </div>
            </div>
        </div>

        <!-- Execution Tab -->
        <div class="tab-content" id="execution">
            <div class="section">
                <h3>Automation Execution Control</h3>
                <div class="execution-controls">
                    <button class="btn btn-success" id="runExecution">Run Automation</button>
                    <button class="btn btn-warning" id="pauseExecution">Pause</button>
                    <button class="btn btn-danger" id="stopExecution">Stop</button>
                </div>
                <div class="execution-status" id="executionStatus">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="status-text" id="executionStatusText">Ready to execute automation</div>
                </div>
                <div class="execution-log" id="executionLog">
                    <!-- Execution logs will appear here -->
                </div>
            </div>
        </div>
    </div>

    <script src="scripts/api-service.js"></script>
    <script src="scripts/popup.js"></script>
</body>
</html>
