// Venus Auto Fill - Content Script

class VenusAutoFillContent {
    constructor() {
        this.isExecuting = false;
        this.isPaused = false;
        this.currentExecution = null;
        this.executionData = null;
        this.flowEvents = [];
        
        this.init();
    }

    init() {
        this.setupMessageListener();
        console.log('Venus Auto Fill content script initialized on:', window.location.href);
    }

    setupMessageListener() {
        chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
            this.handleMessage(message, sender, sendResponse);
            return true; // Keep message channel open for async responses
        });
    }

    async handleMessage(message, sender, sendResponse) {
        try {
            switch (message.action) {
                case 'executeFlow':
                    await this.executeFlow(message.flowEvents, message.data, sendResponse);
                    break;

                case 'pauseExecution':
                    this.pauseExecution();
                    sendResponse({ success: true });
                    break;

                case 'stopExecution':
                    this.stopExecution();
                    sendResponse({ success: true });
                    break;

                case 'highlightElement':
                    this.highlightElement(message.selector);
                    sendResponse({ success: true });
                    break;

                case 'getPageInfo':
                    sendResponse(this.getPageInfo());
                    break;

                default:
                    console.warn('Unknown message action:', message.action);
                    sendResponse({ success: false, error: 'Unknown action' });
            }
        } catch (error) {
            console.error('Error handling message:', error);
            sendResponse({ success: false, error: error.message });
        }
    }

    async executeFlow(flowEvents, data, sendResponse) {
        if (this.isExecuting) {
            sendResponse({ success: false, error: 'Execution already in progress' });
            return;
        }

        this.isExecuting = true;
        this.isPaused = false;
        this.flowEvents = flowEvents;
        this.executionData = data;

        try {
            this.logEvent('Starting flow execution', 'info');
            sendResponse({ success: true, message: 'Execution started' });

            // Execute flow for each data record
            for (let i = 0; i < data.length && this.isExecuting; i++) {
                if (this.isPaused) {
                    await this.waitForResume();
                }

                if (!this.isExecuting) break;

                this.logEvent(`Processing record ${i + 1}/${data.length}: ${data[i].employeeName}`, 'info');
                
                await this.executeFlowForRecord(data[i], i);
                
                // Update progress
                const progress = ((i + 1) / data.length) * 100;
                this.notifyProgress(progress);
                
                // Small delay between records
                await this.delay(500);
            }

            if (this.isExecuting) {
                this.logEvent('Flow execution completed successfully', 'success');
                this.notifyCompletion(true);
            }

        } catch (error) {
            this.logEvent(`Flow execution failed: ${error.message}`, 'error');
            this.notifyCompletion(false, error.message);
        } finally {
            this.isExecuting = false;
            this.isPaused = false;
        }
    }

    async executeFlowForRecord(record, recordIndex) {
        for (let i = 0; i < this.flowEvents.length && this.isExecuting; i++) {
            if (this.isPaused) {
                await this.waitForResume();
            }

            if (!this.isExecuting) break;

            const event = this.flowEvents[i];
            await this.executeEvent(event, record, recordIndex);
        }
    }

    async executeEvent(event, record, recordIndex) {
        try {
            this.logEvent(`Executing ${event.type}: ${JSON.stringify(event)}`, 'info');

            switch (event.type) {
                case 'click':
                    await this.executeClick(event, record, recordIndex);
                    break;

                case 'input':
                    await this.executeInput(event, record, recordIndex);
                    break;

                case 'wait':
                    await this.executeWait(event);
                    break;

                case 'scroll':
                    await this.executeScroll(event);
                    break;

                case 'select':
                    await this.executeSelect(event, record, recordIndex);
                    break;

                default:
                    throw new Error(`Unknown event type: ${event.type}`);
            }

            this.logEvent(`Event ${event.type} completed successfully`, 'success');

        } catch (error) {
            this.logEvent(`Event ${event.type} failed: ${error.message}`, 'error');
            throw error;
        }
    }

    async executeClick(event, record, recordIndex) {
        const element = await this.findElement(event.selector);
        if (!element) {
            throw new Error(`Element not found: ${event.selector}`);
        }

        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.delay(300);

        // Highlight element briefly
        this.highlightElement(event.selector, 500);

        // Click the element
        element.click();
        await this.delay(200);
    }

    async executeInput(event, record, recordIndex) {
        const element = await this.findElement(event.selector);
        if (!element) {
            throw new Error(`Input element not found: ${event.selector}`);
        }

        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.delay(300);

        // Highlight element briefly
        this.highlightElement(event.selector, 500);

        // Clear existing value
        element.focus();
        element.select();
        
        // Get the value to input (can include placeholders)
        const value = this.processValuePlaceholders(event.value, record, recordIndex);
        
        // Type the value
        await this.typeText(element, value);
        
        // Trigger change event
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
    }

    async executeWait(event) {
        const duration = event.duration || 1000;
        this.logEvent(`Waiting for ${duration}ms`, 'info');
        await this.delay(duration);
    }

    async executeScroll(event) {
        const x = event.x || 0;
        const y = event.y || 0;
        
        if (event.element) {
            const element = await this.findElement(event.element);
            if (element) {
                element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        } else {
            window.scrollTo({ left: x, top: y, behavior: 'smooth' });
        }
        
        await this.delay(500);
    }

    async executeSelect(event, record, recordIndex) {
        const element = await this.findElement(event.selector);
        if (!element) {
            throw new Error(`Select element not found: ${event.selector}`);
        }

        // Scroll element into view
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        await this.delay(300);

        // Highlight element briefly
        this.highlightElement(event.selector, 500);

        const value = this.processValuePlaceholders(event.value, record, recordIndex);
        
        if (element.tagName.toLowerCase() === 'select') {
            element.value = value;
            element.dispatchEvent(new Event('change', { bubbles: true }));
        } else {
            throw new Error('Element is not a select element');
        }
    }

    processValuePlaceholders(value, record, recordIndex) {
        if (!value || typeof value !== 'string') {
            return value;
        }

        // Replace placeholders with actual data
        let processedValue = value;
        
        // Employee data placeholders
        processedValue = processedValue.replace(/\{employeeId\}/g, record.employeeId || '');
        processedValue = processedValue.replace(/\{employeeName\}/g, record.employeeName || '');
        processedValue = processedValue.replace(/\{recordIndex\}/g, recordIndex.toString());
        
        // Date placeholders
        const today = new Date();
        processedValue = processedValue.replace(/\{today\}/g, today.toISOString().split('T')[0]);
        processedValue = processedValue.replace(/\{timestamp\}/g, Date.now().toString());
        
        // Attendance data placeholders (for specific days)
        const dayMatch = value.match(/\{day(\d+)\.regular\}/g);
        if (dayMatch) {
            dayMatch.forEach(match => {
                const dayNum = parseInt(match.match(/\d+/)[0]) - 1;
                if (record.attendanceData && record.attendanceData[dayNum]) {
                    processedValue = processedValue.replace(match, record.attendanceData[dayNum].regular.toString());
                }
            });
        }
        
        const overtimeMatch = value.match(/\{day(\d+)\.overtime\}/g);
        if (overtimeMatch) {
            overtimeMatch.forEach(match => {
                const dayNum = parseInt(match.match(/\d+/)[0]) - 1;
                if (record.attendanceData && record.attendanceData[dayNum]) {
                    processedValue = processedValue.replace(match, record.attendanceData[dayNum].overtime.toString());
                }
            });
        }

        return processedValue;
    }

    async findElement(selector, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            const element = document.querySelector(selector);
            if (element) {
                return element;
            }
            await this.delay(100);
        }
        
        return null;
    }

    async typeText(element, text) {
        element.value = '';
        
        for (const char of text) {
            if (!this.isExecuting) break;
            
            element.value += char;
            element.dispatchEvent(new Event('input', { bubbles: true }));
            await this.delay(50); // Simulate human typing speed
        }
    }

    highlightElement(selector, duration = 3000) {
        const element = document.querySelector(selector);
        if (!element) return;

        const originalOutline = element.style.outline;
        const originalOutlineOffset = element.style.outlineOffset;
        
        element.style.outline = '3px solid #ff0000';
        element.style.outlineOffset = '2px';
        
        setTimeout(() => {
            element.style.outline = originalOutline;
            element.style.outlineOffset = originalOutlineOffset;
        }, duration);
    }

    pauseExecution() {
        if (this.isExecuting) {
            this.isPaused = true;
            this.logEvent('Execution paused', 'warning');
        }
    }

    stopExecution() {
        this.isExecuting = false;
        this.isPaused = false;
        this.logEvent('Execution stopped', 'error');
    }

    async waitForResume() {
        while (this.isPaused && this.isExecuting) {
            await this.delay(100);
        }
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    getPageInfo() {
        return {
            url: window.location.href,
            title: document.title,
            domain: window.location.hostname,
            timestamp: new Date().toISOString()
        };
    }

    logEvent(message, level = 'info') {
        console.log(`[Venus Auto Fill] [${level.toUpperCase()}] ${message}`);
        
        // Send log to background script
        chrome.runtime.sendMessage({
            action: 'logExecution',
            event: message,
            level: level,
            timestamp: new Date().toISOString()
        });
    }

    notifyProgress(progress) {
        // Send progress update to popup
        chrome.runtime.sendMessage({
            action: 'executionProgress',
            progress: progress
        });
    }

    notifyCompletion(success, error = null) {
        // Send completion notification to popup
        chrome.runtime.sendMessage({
            action: 'executionComplete',
            success: success,
            error: error
        });
    }
}

// Initialize content script
new VenusAutoFillContent();
